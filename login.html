<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مطعم بازوكا</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .login-header {
            margin-bottom: 2rem;
        }
        
        .logo {
            width: 100px;
            height: 100px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2.5rem;
        }
        
        .login-header h1 {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: var(--text-light);
            font-size: 1.1rem;
        }
        
        .login-options {
            display: grid;
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .login-option {
            background: var(--white);
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 2rem;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            color: inherit;
        }
        
        .login-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
        }
        
        .option-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.8rem;
            color: white;
        }
        
        .customer-option .option-icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }
        
        .admin-option .option-icon {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }
        
        .staff-option .option-icon {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }
        
        .option-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .option-description {
            font-size: 0.95rem;
            color: var(--text-light);
            line-height: 1.5;
        }
        
        .back-to-site {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: var(--transition);
            backdrop-filter: blur(10px);
        }
        
        .back-to-site:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .features-list {
            text-align: right;
            margin-top: 1rem;
        }
        
        .features-list li {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-light);
        }
        
        .features-list i {
            color: var(--primary-color);
            margin-left: 0.5rem;
            width: 16px;
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .back-to-site {
                position: static;
                display: block;
                margin-bottom: 2rem;
            }
            
            .login-header h1 {
                font-size: 1.8rem;
            }
            
            .option-title {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-to-site">
        <i class="fas fa-arrow-right"></i> العودة للموقع
    </a>

    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-utensils"></i>
            </div>
            <h1>مرحباً بك في بازوكا</h1>
            <p>اختر نوع حسابك للمتابعة</p>
        </div>

        <div class="login-options">
            <a href="index.html" class="login-option customer-option">
                <div class="option-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="option-title">عميل</div>
                <div class="option-description">
                    تصفح القائمة، اطلب الطعام، واحجز الطاولات
                </div>
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> تصفح قائمة الطعام</li>
                    <li><i class="fas fa-check"></i> طلب الطعام أونلاين</li>
                    <li><i class="fas fa-check"></i> حجز الطاولات</li>
                    <li><i class="fas fa-check"></i> تتبع الطلبات</li>
                </ul>
            </a>

            <a href="admin-login.html" class="login-option admin-option">
                <div class="option-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="option-title">مدير</div>
                <div class="option-description">
                    الوصول الكامل للوحة التحكم وإدارة النظام
                </div>
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> لوحة التحكم المتقدمة</li>
                    <li><i class="fas fa-check"></i> إدارة الطلبات والحجوزات</li>
                    <li><i class="fas fa-check"></i> التقارير والإحصائيات</li>
                    <li><i class="fas fa-check"></i> إدارة الموظفين والعملاء</li>
                </ul>
            </a>

            <a href="dashboard.html" class="login-option staff-option">
                <div class="option-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="option-title">موظف</div>
                <div class="option-description">
                    إدارة الطلبات والعمليات اليومية
                </div>
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> إدارة الطلبات</li>
                    <li><i class="fas fa-check"></i> تحديث حالة الطلبات</li>
                    <li><i class="fas fa-check"></i> إدارة الحجوزات</li>
                    <li><i class="fas fa-check"></i> تقارير المبيعات</li>
                </ul>
            </a>
        </div>

        <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e9ecef;">
            <p style="color: var(--text-light); font-size: 0.9rem;">
                <i class="fas fa-shield-alt" style="color: var(--primary-color); margin-left: 0.5rem;"></i>
                جميع البيانات محمية بأعلى معايير الأمان
            </p>
        </div>
    </div>

    <script>
        // Add click animations
        document.querySelectorAll('.login-option').forEach(option => {
            option.addEventListener('click', function(e) {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Add hover effects for icons
        document.querySelectorAll('.option-icon').forEach(icon => {
            icon.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1) rotate(5deg)';
            });
            
            icon.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) rotate(0deg)';
            });
        });

        // Animate elements on load
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.login-option');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.5s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
