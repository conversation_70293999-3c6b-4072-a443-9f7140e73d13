// Advanced Dashboard System for Bazooka Restaurant

// Dashboard state
let dashboardState = {
    isFullscreen: false,
    sidebarCollapsed: false,
    currentSection: 'dashboard',
    realTimeData: {
        newOrders: 0,
        preparingOrders: 5,
        readyOrders: 3,
        onlineUsers: 24
    },
    charts: {},
    notifications: [],
    activities: []
};

// Sample data for dashboard
const sampleData = {
    salesData: [
        { day: 'السبت', sales: 4500, orders: 45 },
        { day: 'الأحد', sales: 5200, orders: 52 },
        { day: 'الاثنين', sales: 3800, orders: 38 },
        { day: 'الثلاثاء', sales: 4100, orders: 41 },
        { day: 'الأربعاء', sales: 4800, orders: 48 },
        { day: 'الخميس', sales: 5500, orders: 55 },
        { day: 'الجمعة', sales: 6200, orders: 62 }
    ],
    popularItems: [
        { name: 'مشاوي مشكلة', orders: 45, percentage: 25 },
        { name: 'برياني لحم', orders: 32, percentage: 18 },
        { name: 'فروج مشوي', orders: 28, percentage: 16 },
        { name: 'كنافة نابلسية', orders: 24, percentage: 13 },
        { name: 'حمص بالطحينة', orders: 19, percentage: 11 }
    ],
    activities: [
        {
            id: 1,
            type: 'order',
            title: 'طلب جديد',
            description: 'طلب #BZ001234 من أحمد محمد',
            time: 'منذ دقيقتين',
            icon: 'fas fa-shopping-cart',
            color: '#007bff'
        },
        {
            id: 2,
            type: 'reservation',
            title: 'حجز جديد',
            description: 'حجز طاولة لـ 4 أشخاص - سارة أحمد',
            time: 'منذ 5 دقائق',
            icon: 'fas fa-calendar-plus',
            color: '#28a745'
        },
        {
            id: 3,
            type: 'payment',
            title: 'دفعة مستلمة',
            description: 'تم استلام دفعة 125.50 ريال',
            time: 'منذ 8 دقائق',
            icon: 'fas fa-credit-card',
            color: '#ffc107'
        },
        {
            id: 4,
            type: 'review',
            title: 'تقييم جديد',
            description: 'تقييم 5 نجوم من محمد السعد',
            time: 'منذ 12 دقيقة',
            icon: 'fas fa-star',
            color: '#fd7e14'
        },
        {
            id: 5,
            type: 'inventory',
            title: 'تنبيه مخزون',
            description: 'مخزون الدجاج منخفض (5 كيلو متبقي)',
            time: 'منذ 15 دقيقة',
            icon: 'fas fa-exclamation-triangle',
            color: '#dc3545'
        }
    ],
    notifications: [
        {
            id: 1,
            title: 'طلب جديد',
            message: 'طلب جديد من أحمد محمد بقيمة 125.50 ريال',
            time: 'منذ دقيقتين',
            type: 'order',
            unread: true
        },
        {
            id: 2,
            title: 'حجز طاولة',
            message: 'حجز جديد لطاولة 4 أشخاص اليوم الساعة 7:00 مساءً',
            time: 'منذ 5 دقائق',
            type: 'reservation',
            unread: true
        },
        {
            id: 3,
            title: 'تقييم جديد',
            message: 'تقييم 5 نجوم من محمد السعد مع تعليق إيجابي',
            time: 'منذ 12 دقيقة',
            type: 'review',
            unread: false
        },
        {
            id: 4,
            title: 'تنبيه مخزون',
            message: 'مخزون الدجاج منخفض، يرجى إعادة التموين',
            time: 'منذ 15 دقيقة',
            type: 'warning',
            unread: true
        },
        {
            id: 5,
            title: 'تحديث النظام',
            message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.0',
            time: 'منذ ساعة',
            type: 'system',
            unread: false
        }
    ]
};

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    loadDashboardData();
    startRealTimeUpdates();
});

// Initialize dashboard
function initializeDashboard() {
    console.log('🚀 Advanced Dashboard Initialized');
    
    // Check authentication
    checkDashboardAuth();
    
    // Initialize charts
    initializeCharts();
    
    // Load initial data
    loadActivities();
    loadNotifications();
    
    // Setup search functionality
    setupGlobalSearch();
}

// Check dashboard authentication
function checkDashboardAuth() {
    const adminSession = localStorage.getItem('adminSession');
    
    if (!adminSession) {
        window.location.href = 'admin-login.html';
        return;
    }
    
    try {
        const session = JSON.parse(adminSession);
        const now = new Date().getTime();
        const sessionAge = now - session.timestamp;
        const maxAge = session.remember ? 30 * 24 * 60 * 60 * 1000 : 8 * 60 * 60 * 1000;
        
        if (sessionAge > maxAge) {
            localStorage.removeItem('adminSession');
            showNotification('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning');
            setTimeout(() => {
                window.location.href = 'admin-login.html';
            }, 2000);
        }
    } catch (error) {
        console.error('Error checking auth:', error);
        window.location.href = 'admin-login.html';
    }
}

// Setup event listeners
function setupEventListeners() {
    // Sidebar toggle
    document.addEventListener('click', function(e) {
        if (e.target.closest('.sidebar-toggle')) {
            toggleSidebar();
        }
    });
    
    // Close panels when clicking outside
    document.addEventListener('click', function(e) {
        const notificationsPanel = document.getElementById('notifications-panel');
        const quickActionsPanel = document.getElementById('quick-actions-panel');
        
        if (!e.target.closest('.nav-btn') && !e.target.closest('.notifications-panel')) {
            notificationsPanel.classList.remove('open');
        }
        
        if (!e.target.closest('.nav-btn') && !e.target.closest('.quick-actions-panel')) {
            quickActionsPanel.classList.remove('open');
        }
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            document.getElementById('global-search').focus();
        }
        
        // Escape to close panels
        if (e.key === 'Escape') {
            document.getElementById('notifications-panel').classList.remove('open');
            document.getElementById('quick-actions-panel').classList.remove('open');
        }
    });
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    
    dashboardState.sidebarCollapsed = !dashboardState.sidebarCollapsed;
    
    if (dashboardState.sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    } else {
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('expanded');
    }
}

// Show section
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from menu items
    document.querySelectorAll('.menu-section li').forEach(item => {
        item.classList.remove('active');
    });
    
    // Show selected section
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.add('active');
    }
    
    // Add active class to menu item
    const menuItem = document.querySelector(`a[href="#${sectionId}"]`);
    if (menuItem) {
        menuItem.closest('li').classList.add('active');
    }
    
    dashboardState.currentSection = sectionId;
    
    // Update breadcrumb
    updateBreadcrumb(sectionId);
}

// Update breadcrumb
function updateBreadcrumb(sectionId) {
    const breadcrumb = document.querySelector('.breadcrumb-item');
    const sectionNames = {
        'dashboard': 'لوحة التحكم',
        'analytics': 'التحليلات المتقدمة',
        'orders': 'إدارة الطلبات',
        'menu': 'إدارة القائمة',
        'reservations': 'الحجوزات',
        'inventory': 'إدارة المخزون',
        'customers': 'العملاء',
        'staff': 'الموظفين',
        'suppliers': 'الموردين',
        'finance': 'التقارير المالية',
        'expenses': 'المصروفات',
        'taxes': 'الضرائب',
        'settings': 'الإعدادات',
        'security': 'الأمان',
        'logs': 'سجلات النظام'
    };
    
    if (breadcrumb) {
        breadcrumb.textContent = sectionNames[sectionId] || 'غير معروف';
    }
}

// Toggle notifications
function toggleNotifications() {
    const panel = document.getElementById('notifications-panel');
    const quickActionsPanel = document.getElementById('quick-actions-panel');
    
    // Close quick actions if open
    quickActionsPanel.classList.remove('open');
    
    // Toggle notifications
    panel.classList.toggle('open');
}

// Toggle quick actions
function toggleQuickActions() {
    const panel = document.getElementById('quick-actions-panel');
    const notificationsPanel = document.getElementById('notifications-panel');
    
    // Close notifications if open
    notificationsPanel.classList.remove('open');
    
    // Toggle quick actions
    panel.classList.toggle('open');
}

// Toggle fullscreen
function toggleFullscreen() {
    if (!dashboardState.isFullscreen) {
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
        } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.msRequestFullscreen) {
            document.documentElement.msRequestFullscreen();
        }
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
    
    dashboardState.isFullscreen = !dashboardState.isFullscreen;
    
    // Update button icon
    const fullscreenBtn = document.querySelector('[onclick="toggleFullscreen()"] i');
    if (fullscreenBtn) {
        fullscreenBtn.className = dashboardState.isFullscreen ? 'fas fa-compress' : 'fas fa-expand';
    }
}

// Toggle user menu
function toggleUserMenu() {
    // This will be implemented when user menu dropdown is added
    console.log('User menu toggled');
}

// Initialize charts
function initializeCharts() {
    // Revenue mini chart
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        dashboardState.charts.revenue = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', '', ''],
                datasets: [{
                    data: [4500, 5200, 3800, 4100, 4800, 5500, 6200],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                elements: { point: { radius: 0 } }
            }
        });
    }
    
    // Orders mini chart
    const ordersCtx = document.getElementById('ordersChart');
    if (ordersCtx) {
        dashboardState.charts.orders = new Chart(ordersCtx, {
            type: 'bar',
            data: {
                labels: ['', '', '', '', '', '', ''],
                datasets: [{
                    data: [45, 52, 38, 41, 48, 55, 62],
                    backgroundColor: '#007bff',
                    borderRadius: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: false },
                    y: { display: false }
                }
            }
        });
    }
    
    // Customers mini chart
    const customersCtx = document.getElementById('customersChart');
    if (customersCtx) {
        dashboardState.charts.customers = new Chart(customersCtx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [75, 25],
                    backgroundColor: ['#fd7e14', '#e9ecef'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                cutout: '70%'
            }
        });
    }
    
    // Main sales chart
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        dashboardState.charts.sales = new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: sampleData.salesData.map(item => item.day),
                datasets: [{
                    label: 'المبيعات (ريال)',
                    data: sampleData.salesData.map(item => item.sales),
                    borderColor: '#ff6b35',
                    backgroundColor: 'rgba(255, 107, 53, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: '#f0f0f0' },
                        ticks: { color: '#6c757d' }
                    },
                    x: {
                        grid: { display: false },
                        ticks: { color: '#6c757d' }
                    }
                }
            }
        });
    }
    
    // Popular items chart
    const popularItemsCtx = document.getElementById('popularItemsChart');
    if (popularItemsCtx) {
        dashboardState.charts.popularItems = new Chart(popularItemsCtx, {
            type: 'doughnut',
            data: {
                labels: sampleData.popularItems.map(item => item.name),
                datasets: [{
                    data: sampleData.popularItems.map(item => item.orders),
                    backgroundColor: [
                        '#ff6b35',
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#fd7e14'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { usePointStyle: true }
                    }
                }
            }
        });
    }
}

// Load dashboard data
function loadDashboardData() {
    // Simulate loading data
    setTimeout(() => {
        updateStats();
        updateCharts();
    }, 500);
}

// Update statistics
function updateStats() {
    // Animate stat values
    animateValue('revenue', 0, 45280, 2000);
    animateValue('orders', 0, 127, 1500);
    animateValue('customers', 0, 1234, 1800);
    
    // Update rating stars
    updateRatingStars(4.8);
}

// Animate value
function animateValue(elementClass, start, end, duration) {
    const element = document.querySelector(`.stat-card.${elementClass} .stat-value`);
    if (!element) return;
    
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= end) {
            current = end;
            clearInterval(timer);
        }
        
        if (elementClass === 'revenue') {
            element.textContent = Math.floor(current).toLocaleString() + ' ريال';
        } else {
            element.textContent = Math.floor(current).toLocaleString();
        }
    }, 16);
}

// Update rating stars
function updateRatingStars(rating) {
    const stars = document.querySelectorAll('.rating-stars i');
    const fullStars = Math.floor(rating);
    
    stars.forEach((star, index) => {
        if (index < fullStars) {
            star.className = 'fas fa-star';
        } else {
            star.className = 'far fa-star';
        }
    });
}

// Update charts
function updateCharts() {
    // Update charts with new data if needed
    Object.values(dashboardState.charts).forEach(chart => {
        if (chart && chart.update) {
            chart.update();
        }
    });
}

// Load activities
function loadActivities() {
    const activityList = document.getElementById('activity-list');
    if (!activityList) return;
    
    dashboardState.activities = sampleData.activities;
    
    activityList.innerHTML = dashboardState.activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon" style="background: ${activity.color}">
                <i class="${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${activity.title}</div>
                <div class="activity-description">${activity.description}</div>
                <div class="activity-time">${activity.time}</div>
            </div>
        </div>
    `).join('');
}

// Load notifications
function loadNotifications() {
    const notificationsList = document.getElementById('notifications-list');
    if (!notificationsList) return;
    
    dashboardState.notifications = sampleData.notifications;
    
    // Update notification badge
    const unreadCount = dashboardState.notifications.filter(n => n.unread).length;
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.textContent = unreadCount;
        badge.style.display = unreadCount > 0 ? 'flex' : 'none';
    }
    
    notificationsList.innerHTML = dashboardState.notifications.map(notification => `
        <div class="notification-item ${notification.unread ? 'unread' : ''}" onclick="markNotificationRead(${notification.id})">
            <div class="notification-icon">
                <i class="fas fa-${getNotificationIcon(notification.type)}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${notification.time}</div>
            </div>
        </div>
    `).join('');
}

// Get notification icon
function getNotificationIcon(type) {
    const icons = {
        'order': 'shopping-cart',
        'reservation': 'calendar-plus',
        'review': 'star',
        'warning': 'exclamation-triangle',
        'system': 'cog'
    };
    return icons[type] || 'bell';
}

// Mark notification as read
function markNotificationRead(notificationId) {
    const notification = dashboardState.notifications.find(n => n.id === notificationId);
    if (notification) {
        notification.unread = false;
        loadNotifications();
    }
}

// Mark all notifications as read
function markAllRead() {
    dashboardState.notifications.forEach(notification => {
        notification.unread = false;
    });
    loadNotifications();
    showNotification('تم تعيين جميع الإشعارات كمقروءة', 'success');
}

// Setup global search
function setupGlobalSearch() {
    const searchInput = document.getElementById('global-search');
    const suggestions = document.getElementById('search-suggestions');
    
    if (!searchInput || !suggestions) return;
    
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        if (query.length < 2) {
            suggestions.style.display = 'none';
            return;
        }
        
        // Simulate search results
        const results = [
            { title: 'الطلبات', description: 'إدارة جميع الطلبات', action: () => showSection('orders') },
            { title: 'القائمة', description: 'إدارة قائمة الطعام', action: () => showSection('menu') },
            { title: 'العملاء', description: 'إدارة العملاء', action: () => showSection('customers') },
            { title: 'التقارير', description: 'عرض التقارير المالية', action: () => showSection('finance') }
        ].filter(item => 
            item.title.includes(query) || item.description.includes(query)
        );
        
        if (results.length > 0) {
            suggestions.innerHTML = results.map(result => `
                <div class="search-result" onclick="executeSearchAction(${results.indexOf(result)})">
                    <div class="result-title">${result.title}</div>
                    <div class="result-description">${result.description}</div>
                </div>
            `).join('');
            suggestions.style.display = 'block';
        } else {
            suggestions.style.display = 'none';
        }
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-container')) {
            suggestions.style.display = 'none';
        }
    });
}

// Start real-time updates
function startRealTimeUpdates() {
    // Simulate real-time data updates
    setInterval(() => {
        updateRealTimeData();
    }, 5000); // Update every 5 seconds
}

// Update real-time data
function updateRealTimeData() {
    // Simulate random changes
    dashboardState.realTimeData.newOrders = Math.floor(Math.random() * 5);
    dashboardState.realTimeData.onlineUsers = 20 + Math.floor(Math.random() * 10);
    
    // Update UI
    const newOrdersElement = document.getElementById('newOrders');
    const onlineUsersElement = document.getElementById('onlineUsers');
    
    if (newOrdersElement) {
        newOrdersElement.textContent = dashboardState.realTimeData.newOrders;
        if (dashboardState.realTimeData.newOrders > 0) {
            newOrdersElement.style.animation = 'pulse 1s ease-in-out';
            setTimeout(() => {
                newOrdersElement.style.animation = '';
            }, 1000);
        }
    }
    
    if (onlineUsersElement) {
        onlineUsersElement.textContent = dashboardState.realTimeData.onlineUsers;
    }
}

// Dashboard action functions
function refreshDashboard() {
    showNotification('جاري تحديث لوحة التحكم...', 'info');
    
    setTimeout(() => {
        loadDashboardData();
        loadActivities();
        loadNotifications();
        showNotification('تم تحديث لوحة التحكم بنجاح', 'success');
    }, 1500);
}

function exportDashboard() {
    showNotification('جاري تصدير البيانات...', 'info');
    
    setTimeout(() => {
        showNotification('تم تصدير البيانات بنجاح', 'success');
    }, 2000);
}

function refreshActivity() {
    loadActivities();
    showNotification('تم تحديث النشاطات', 'success');
}

function refreshPopularItems() {
    if (dashboardState.charts.popularItems) {
        dashboardState.charts.popularItems.update();
    }
    showNotification('تم تحديث الأطباق الشائعة', 'success');
}

// Quick action functions
function addNewOrder() {
    showNotification('فتح نافذة طلب جديد...', 'info');
    // This would open a modal or navigate to order creation
}

function addNewItem() {
    showNotification('فتح نافذة إضافة طبق جديد...', 'info');
    // This would open a modal or navigate to menu item creation
}

function addReservation() {
    showNotification('فتح نافذة حجز جديد...', 'info');
    // This would open a modal or navigate to reservation creation
}

function viewReports() {
    showSection('finance');
    showNotification('عرض التقارير المالية', 'info');
}

function manageStaff() {
    showSection('staff');
    showNotification('إدارة الموظفين', 'info');
}

function systemSettings() {
    showSection('settings');
    showNotification('إعدادات النظام', 'info');
}

// Add CSS for search results
const searchStyles = document.createElement('style');
searchStyles.textContent = `
    .search-result {
        padding: 1rem;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: var(--transition);
    }
    
    .search-result:hover {
        background: var(--bg-light);
    }
    
    .search-result:last-child {
        border-bottom: none;
    }
    
    .result-title {
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.25rem;
    }
    
    .result-description {
        font-size: 0.9rem;
        color: var(--text-light);
    }
`;
document.head.appendChild(searchStyles);
