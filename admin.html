<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة مطعم بازوكا</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-nav">
            <div class="admin-logo">
                <i class="fas fa-utensils"></i>
                <span>بازوكا - الإدارة</span>
            </div>
            <div class="admin-user">
                <span>مرحباً، المدير</span>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل خروج
                </button>
            </div>
        </div>
    </header>

    <!-- Admin Sidebar -->
    <aside class="admin-sidebar">
        <nav class="admin-menu">
            <ul>
                <li class="active">
                    <a href="#dashboard" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                <li>
                    <a href="#orders" onclick="showSection('orders')">
                        <i class="fas fa-shopping-bag"></i>
                        <span>الطلبات</span>
                        <span class="badge">5</span>
                    </a>
                </li>
                <li>
                    <a href="#menu-management" onclick="showSection('menu-management')">
                        <i class="fas fa-utensils"></i>
                        <span>إدارة القائمة</span>
                    </a>
                </li>
                <li>
                    <a href="#reservations" onclick="showSection('reservations')">
                        <i class="fas fa-calendar-alt"></i>
                        <span>الحجوزات</span>
                        <span class="badge">3</span>
                    </a>
                </li>
                <li>
                    <a href="#customers" onclick="showSection('customers')">
                        <i class="fas fa-users"></i>
                        <span>العملاء</span>
                    </a>
                </li>
                <li>
                    <a href="#reports" onclick="showSection('reports')">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </a>
                </li>
                <li>
                    <a href="#settings" onclick="showSection('settings')">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <!-- Dashboard Section -->
        <section id="dashboard" class="admin-section active">
            <div class="section-header">
                <h1>لوحة التحكم</h1>
                <p>نظرة عامة على أداء المطعم</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="stat-info">
                        <h3>الطلبات اليوم</h3>
                        <span class="stat-number">24</span>
                        <span class="stat-change positive">+12%</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3>المبيعات اليوم</h3>
                        <span class="stat-number">2,450 ريال</span>
                        <span class="stat-change positive">+8%</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3>العملاء الجدد</h3>
                        <span class="stat-number">8</span>
                        <span class="stat-change positive">+15%</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-info">
                        <h3>متوسط التقييم</h3>
                        <span class="stat-number">4.8</span>
                        <span class="stat-change neutral">0%</span>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="dashboard-widgets">
                <div class="widget">
                    <div class="widget-header">
                        <h3>الطلبات الأخيرة</h3>
                        <a href="#orders" onclick="showSection('orders')">عرض الكل</a>
                    </div>
                    <div class="widget-content">
                        <div class="recent-orders" id="recent-orders">
                            <!-- Orders will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="widget">
                    <div class="widget-header">
                        <h3>الأطباق الأكثر طلباً</h3>
                    </div>
                    <div class="widget-content">
                        <div class="popular-items" id="popular-items">
                            <!-- Popular items will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Orders Section -->
        <section id="orders" class="admin-section">
            <div class="section-header">
                <h1>إدارة الطلبات</h1>
                <div class="section-actions">
                    <select id="order-filter">
                        <option value="all">جميع الطلبات</option>
                        <option value="pending">قيد الانتظار</option>
                        <option value="preparing">قيد التحضير</option>
                        <option value="ready">جاهز للتوصيل</option>
                        <option value="delivered">تم التوصيل</option>
                    </select>
                    <button class="btn btn-primary" onclick="refreshOrders()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <div class="orders-container">
                <div class="orders-grid" id="orders-grid">
                    <!-- Orders will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Menu Management Section -->
        <section id="menu-management" class="admin-section">
            <div class="section-header">
                <h1>إدارة القائمة</h1>
                <button class="btn btn-primary" onclick="showAddItemModal()">
                    <i class="fas fa-plus"></i> إضافة طبق جديد
                </button>
            </div>

            <div class="menu-filters">
                <button class="filter-btn active" data-category="all">الكل</button>
                <button class="filter-btn" data-category="appetizers">المقبلات</button>
                <button class="filter-btn" data-category="main">الأطباق الرئيسية</button>
                <button class="filter-btn" data-category="desserts">الحلويات</button>
                <button class="filter-btn" data-category="drinks">المشروبات</button>
            </div>

            <div class="menu-items-grid" id="admin-menu-grid">
                <!-- Menu items will be loaded here -->
            </div>
        </section>

        <!-- Reservations Section -->
        <section id="reservations" class="admin-section">
            <div class="section-header">
                <h1>إدارة الحجوزات</h1>
                <div class="section-actions">
                    <input type="date" id="reservation-date" value="">
                    <button class="btn btn-primary" onclick="filterReservations()">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                </div>
            </div>

            <div class="reservations-container">
                <div class="reservations-grid" id="reservations-grid">
                    <!-- Reservations will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Customers Section -->
        <section id="customers" class="admin-section">
            <div class="section-header">
                <h1>إدارة العملاء</h1>
                <div class="section-actions">
                    <input type="search" placeholder="البحث عن عميل..." id="customer-search">
                    <button class="btn btn-secondary" onclick="exportCustomers()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>

            <div class="customers-table-container">
                <table class="customers-table" id="customers-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>عدد الطلبات</th>
                            <th>إجمالي المشتريات</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="customers-tbody">
                        <!-- Customer data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="admin-section">
            <div class="section-header">
                <h1>التقارير والإحصائيات</h1>
                <div class="section-actions">
                    <select id="report-period">
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                        <option value="year">هذا العام</option>
                    </select>
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-chart-line"></i> إنشاء تقرير
                    </button>
                </div>
            </div>

            <div class="reports-grid">
                <div class="report-card">
                    <h3>تقرير المبيعات</h3>
                    <div class="chart-container">
                        <canvas id="sales-chart"></canvas>
                    </div>
                </div>
                <div class="report-card">
                    <h3>الأطباق الأكثر مبيعاً</h3>
                    <div class="chart-container">
                        <canvas id="items-chart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="admin-section">
            <div class="section-header">
                <h1>إعدادات المطعم</h1>
            </div>

            <div class="settings-container">
                <div class="settings-group">
                    <h3>معلومات المطعم</h3>
                    <form class="settings-form">
                        <input type="text" placeholder="اسم المطعم" value="مطعم بازوكا">
                        <input type="text" placeholder="العنوان" value="شارع الملك فهد، الرياض">
                        <input type="tel" placeholder="رقم الهاتف" value="+966 11 123 4567">
                        <input type="email" placeholder="البريد الإلكتروني" value="<EMAIL>">
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </form>
                </div>

                <div class="settings-group">
                    <h3>ساعات العمل</h3>
                    <form class="settings-form">
                        <div class="time-settings">
                            <label>من: <input type="time" value="11:00"></label>
                            <label>إلى: <input type="time" value="24:00"></label>
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </form>
                </div>

                <div class="settings-group">
                    <h3>إعدادات التوصيل</h3>
                    <form class="settings-form">
                        <input type="number" placeholder="رسوم التوصيل" value="15">
                        <input type="number" placeholder="الحد الأدنى للتوصيل المجاني" value="100">
                        <input type="number" placeholder="وقت التوصيل المتوقع (دقيقة)" value="30">
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Add Item Modal -->
    <div class="modal" id="add-item-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة طبق جديد</h3>
                <button class="modal-close" onclick="closeAddItemModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-item-form" class="item-form">
                    <input type="text" name="name" placeholder="اسم الطبق" required>
                    <textarea name="description" placeholder="وصف الطبق" required></textarea>
                    <input type="number" name="price" placeholder="السعر" step="0.01" required>
                    <select name="category" required>
                        <option value="">اختر الفئة</option>
                        <option value="appetizers">المقبلات</option>
                        <option value="main">الأطباق الرئيسية</option>
                        <option value="desserts">الحلويات</option>
                        <option value="drinks">المشروبات</option>
                    </select>
                    <input type="url" name="image" placeholder="رابط الصورة" required>
                    <label class="checkbox-label">
                        <input type="checkbox" name="popular"> طبق مميز
                    </label>
                    <button type="submit" class="btn btn-primary">إضافة الطبق</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="scripts/admin.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html>
