// Admin Panel JavaScript for Bazooka Restaurant

// Admin state
let adminState = {
    currentSection: 'dashboard',
    orders: [],
    reservations: [],
    customers: [],
    menuItems: []
};

// Sample data
const sampleOrders = [
    {
        id: 'BZ001234',
        customerName: 'أحمد محمد',
        phone: '+966501234567',
        items: ['مشاوي مشكلة', 'حمص بالطحينة', 'عصير برتقال'],
        total: 125.50,
        status: 'pending',
        time: '2024-01-15 14:30',
        address: 'حي النخيل، الرياض'
    },
    {
        id: 'BZ001235',
        customerName: 'فاطمة علي',
        phone: '+966507654321',
        items: ['برياني لحم', 'سلطة فتوش'],
        total: 89.00,
        status: 'preparing',
        time: '2024-01-15 14:45',
        address: 'حي الملز، الرياض'
    },
    {
        id: 'BZ001236',
        customerName: 'محمد السعد',
        phone: '+966512345678',
        items: ['فروج مشوي', 'كنافة نابلسية', 'شاي أحمر'],
        total: 98.00,
        status: 'ready',
        time: '2024-01-15 15:00',
        address: 'حي العليا، الرياض'
    }
];

const sampleReservations = [
    {
        id: 'RES001',
        name: 'سارة أحمد',
        phone: '+966501111111',
        email: '<EMAIL>',
        date: '2024-01-16',
        time: '19:00',
        guests: 4,
        notes: 'طاولة بجانب النافذة'
    },
    {
        id: 'RES002',
        name: 'خالد محمد',
        phone: '+966502222222',
        email: '<EMAIL>',
        date: '2024-01-16',
        time: '20:30',
        guests: 2,
        notes: 'ذكرى زواج'
    }
];

const sampleCustomers = [
    {
        id: 1,
        name: 'أحمد محمد',
        phone: '+966501234567',
        email: '<EMAIL>',
        orders: 15,
        totalSpent: 1250.00,
        joinDate: '2023-06-15'
    },
    {
        id: 2,
        name: 'فاطمة علي',
        phone: '+966507654321',
        email: '<EMAIL>',
        orders: 8,
        totalSpent: 680.00,
        joinDate: '2023-08-22'
    }
];

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
    loadSampleData();
    setupAdminEventListeners();
});

// Initialize admin panel
function initializeAdmin() {
    console.log('Admin Panel Initialized');
    showSection('dashboard');
    updateDashboardStats();
}

// Load sample data
function loadSampleData() {
    adminState.orders = sampleOrders;
    adminState.reservations = sampleReservations;
    adminState.customers = sampleCustomers;
    adminState.menuItems = menuData || [];
    
    loadRecentOrders();
    loadPopularItems();
    loadOrders();
    loadReservations();
    loadCustomers();
    loadMenuItems();
}

// Setup event listeners
function setupAdminEventListeners() {
    // Menu filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            filterButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            const category = this.getAttribute('data-category');
            filterMenuItems(category);
        });
    });

    // Order filter
    const orderFilter = document.getElementById('order-filter');
    if (orderFilter) {
        orderFilter.addEventListener('change', function() {
            filterOrders(this.value);
        });
    }

    // Customer search
    const customerSearch = document.getElementById('customer-search');
    if (customerSearch) {
        customerSearch.addEventListener('input', function() {
            searchCustomers(this.value);
        });
    }

    // Add item form
    const addItemForm = document.getElementById('add-item-form');
    if (addItemForm) {
        addItemForm.addEventListener('submit', handleAddItem);
    }
}

// Show section
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from menu items
    document.querySelectorAll('.admin-menu li').forEach(item => {
        item.classList.remove('active');
    });
    
    // Show selected section
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.add('active');
    }
    
    // Add active class to menu item
    const menuItem = document.querySelector(`a[href="#${sectionId}"]`);
    if (menuItem) {
        menuItem.closest('li').classList.add('active');
    }
    
    adminState.currentSection = sectionId;
}

// Update dashboard stats
function updateDashboardStats() {
    const today = new Date().toISOString().split('T')[0];
    const todayOrders = adminState.orders.filter(order => 
        order.time.startsWith(today)
    );
    
    const todaySales = todayOrders.reduce((sum, order) => sum + order.total, 0);
    const newCustomers = adminState.customers.filter(customer => 
        customer.joinDate === today
    ).length;
    
    // Update stat cards
    document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = todayOrders.length;
    document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = `${todaySales.toFixed(2)} ريال`;
    document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = newCustomers;
}

// Load recent orders for dashboard
function loadRecentOrders() {
    const recentOrdersContainer = document.getElementById('recent-orders');
    if (!recentOrdersContainer) return;
    
    const recentOrders = adminState.orders.slice(0, 5);
    
    recentOrdersContainer.innerHTML = recentOrders.map(order => `
        <div class="recent-order-item">
            <div class="order-info">
                <strong>${order.id}</strong>
                <span>${order.customerName}</span>
            </div>
            <div class="order-status ${order.status}">
                ${getStatusText(order.status)}
            </div>
            <div class="order-total">
                ${order.total.toFixed(2)} ريال
            </div>
        </div>
    `).join('');
}

// Load popular items for dashboard
function loadPopularItems() {
    const popularItemsContainer = document.getElementById('popular-items');
    if (!popularItemsContainer) return;
    
    const popularItems = [
        { name: 'مشاوي مشكلة', orders: 45 },
        { name: 'برياني لحم', orders: 32 },
        { name: 'فروج مشوي', orders: 28 },
        { name: 'كنافة نابلسية', orders: 24 },
        { name: 'حمص بالطحينة', orders: 19 }
    ];
    
    popularItemsContainer.innerHTML = popularItems.map(item => `
        <div class="popular-item">
            <span class="item-name">${item.name}</span>
            <span class="item-orders">${item.orders} طلب</span>
        </div>
    `).join('');
}

// Load orders
function loadOrders(filter = 'all') {
    const ordersGrid = document.getElementById('orders-grid');
    if (!ordersGrid) return;
    
    let filteredOrders = adminState.orders;
    if (filter !== 'all') {
        filteredOrders = adminState.orders.filter(order => order.status === filter);
    }
    
    ordersGrid.innerHTML = filteredOrders.map(order => `
        <div class="order-card">
            <div class="order-header">
                <span class="order-number">${order.id}</span>
                <span class="order-status ${order.status}">${getStatusText(order.status)}</span>
            </div>
            <div class="order-customer">
                <strong>${order.customerName}</strong>
                <span>${order.phone}</span>
            </div>
            <div class="order-items">
                ${order.items.join(', ')}
            </div>
            <div class="order-address">
                <i class="fas fa-map-marker-alt"></i> ${order.address}
            </div>
            <div class="order-footer">
                <span class="order-total">${order.total.toFixed(2)} ريال</span>
                <div class="order-actions">
                    ${getOrderActions(order)}
                </div>
            </div>
        </div>
    `).join('');
}

// Get status text in Arabic
function getStatusText(status) {
    const statusMap = {
        'pending': 'قيد الانتظار',
        'preparing': 'قيد التحضير',
        'ready': 'جاهز للتوصيل',
        'delivered': 'تم التوصيل'
    };
    return statusMap[status] || status;
}

// Get order actions based on status
function getOrderActions(order) {
    switch (order.status) {
        case 'pending':
            return `
                <button class="btn-accept" onclick="updateOrderStatus('${order.id}', 'preparing')">قبول</button>
                <button class="btn-reject" onclick="updateOrderStatus('${order.id}', 'cancelled')">رفض</button>
            `;
        case 'preparing':
            return `
                <button class="btn-complete" onclick="updateOrderStatus('${order.id}', 'ready')">جاهز</button>
            `;
        case 'ready':
            return `
                <button class="btn-complete" onclick="updateOrderStatus('${order.id}', 'delivered')">تم التوصيل</button>
            `;
        default:
            return '';
    }
}

// Update order status
function updateOrderStatus(orderId, newStatus) {
    const orderIndex = adminState.orders.findIndex(order => order.id === orderId);
    if (orderIndex > -1) {
        adminState.orders[orderIndex].status = newStatus;
        loadOrders();
        showNotification(`تم تحديث حالة الطلب ${orderId}`, 'success');
    }
}

// Filter orders
function filterOrders(status) {
    loadOrders(status);
}

// Load reservations
function loadReservations() {
    const reservationsGrid = document.getElementById('reservations-grid');
    if (!reservationsGrid) return;
    
    reservationsGrid.innerHTML = adminState.reservations.map(reservation => `
        <div class="reservation-card">
            <div class="reservation-header">
                <span class="reservation-name">${reservation.name}</span>
                <span class="reservation-time">${reservation.date} - ${reservation.time}</span>
            </div>
            <div class="reservation-details">
                <div class="reservation-detail">
                    <i class="fas fa-phone"></i>
                    <span>${reservation.phone}</span>
                </div>
                <div class="reservation-detail">
                    <i class="fas fa-envelope"></i>
                    <span>${reservation.email}</span>
                </div>
                <div class="reservation-detail">
                    <i class="fas fa-users"></i>
                    <span>${reservation.guests} أشخاص</span>
                </div>
            </div>
            ${reservation.notes ? `<div class="reservation-notes">ملاحظات: ${reservation.notes}</div>` : ''}
            <div class="reservation-actions">
                <button class="btn btn-primary btn-sm" onclick="confirmReservation('${reservation.id}')">تأكيد</button>
                <button class="btn btn-secondary btn-sm" onclick="cancelReservation('${reservation.id}')">إلغاء</button>
            </div>
        </div>
    `).join('');
}

// Load customers
function loadCustomers() {
    const customersTable = document.getElementById('customers-tbody');
    if (!customersTable) return;
    
    customersTable.innerHTML = adminState.customers.map(customer => `
        <tr>
            <td>${customer.name}</td>
            <td>${customer.phone}</td>
            <td>${customer.email}</td>
            <td>${customer.orders}</td>
            <td>${customer.totalSpent.toFixed(2)} ريال</td>
            <td>${customer.joinDate}</td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="viewCustomer(${customer.id})">عرض</button>
                <button class="btn btn-secondary btn-sm" onclick="editCustomer(${customer.id})">تعديل</button>
            </td>
        </tr>
    `).join('');
}

// Load menu items for admin
function loadMenuItems(category = 'all') {
    const menuGrid = document.getElementById('admin-menu-grid');
    if (!menuGrid) return;
    
    let filteredItems = adminState.menuItems;
    if (category !== 'all') {
        filteredItems = adminState.menuItems.filter(item => item.category === category);
    }
    
    menuGrid.innerHTML = filteredItems.map(item => `
        <div class="admin-menu-item">
            <img src="${item.image}" alt="${item.name}" loading="lazy">
            <div class="admin-menu-item-content">
                <h3>${item.name}</h3>
                <p>${item.description}</p>
                <div class="admin-menu-item-footer">
                    <span class="price">${item.price.toFixed(2)} ريال</span>
                    <div class="admin-menu-item-actions">
                        <button class="btn-edit" onclick="editMenuItem(${item.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-delete" onclick="deleteMenuItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn-toggle" onclick="toggleMenuItem(${item.id})">
                            <i class="fas fa-eye${item.available === false ? '-slash' : ''}"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Filter menu items
function filterMenuItems(category) {
    loadMenuItems(category);
}

// Show add item modal
function showAddItemModal() {
    const modal = document.getElementById('add-item-modal');
    if (modal) {
        modal.classList.add('show');
    }
}

// Close add item modal
function closeAddItemModal() {
    const modal = document.getElementById('add-item-modal');
    if (modal) {
        modal.classList.remove('show');
    }
}

// Handle add item form
function handleAddItem(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const newItem = {
        id: Date.now(),
        name: formData.get('name'),
        description: formData.get('description'),
        price: parseFloat(formData.get('price')),
        category: formData.get('category'),
        image: formData.get('image'),
        popular: formData.get('popular') === 'on',
        available: true
    };
    
    adminState.menuItems.push(newItem);
    loadMenuItems();
    closeAddItemModal();
    e.target.reset();
    
    showNotification('تم إضافة الطبق بنجاح', 'success');
}

// Edit menu item
function editMenuItem(itemId) {
    const item = adminState.menuItems.find(item => item.id === itemId);
    if (item) {
        // Show edit modal with item data
        showNotification('سيتم إضافة نافذة التعديل قريباً', 'info');
    }
}

// Delete menu item
function deleteMenuItem(itemId) {
    if (confirm('هل أنت متأكد من حذف هذا الطبق؟')) {
        const itemIndex = adminState.menuItems.findIndex(item => item.id === itemId);
        if (itemIndex > -1) {
            adminState.menuItems.splice(itemIndex, 1);
            loadMenuItems();
            showNotification('تم حذف الطبق بنجاح', 'success');
        }
    }
}

// Toggle menu item availability
function toggleMenuItem(itemId) {
    const item = adminState.menuItems.find(item => item.id === itemId);
    if (item) {
        item.available = !item.available;
        loadMenuItems();
        showNotification(`تم ${item.available ? 'تفعيل' : 'إلغاء'} الطبق`, 'success');
    }
}

// Search customers
function searchCustomers(query) {
    const filteredCustomers = adminState.customers.filter(customer =>
        customer.name.toLowerCase().includes(query.toLowerCase()) ||
        customer.phone.includes(query) ||
        customer.email.toLowerCase().includes(query.toLowerCase())
    );
    
    const customersTable = document.getElementById('customers-tbody');
    if (customersTable) {
        customersTable.innerHTML = filteredCustomers.map(customer => `
            <tr>
                <td>${customer.name}</td>
                <td>${customer.phone}</td>
                <td>${customer.email}</td>
                <td>${customer.orders}</td>
                <td>${customer.totalSpent.toFixed(2)} ريال</td>
                <td>${customer.joinDate}</td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="viewCustomer(${customer.id})">عرض</button>
                    <button class="btn btn-secondary btn-sm" onclick="editCustomer(${customer.id})">تعديل</button>
                </td>
            </tr>
        `).join('');
    }
}

// Refresh orders
function refreshOrders() {
    loadOrders();
    showNotification('تم تحديث الطلبات', 'success');
}

// Confirm reservation
function confirmReservation(reservationId) {
    showNotification(`تم تأكيد الحجز ${reservationId}`, 'success');
}

// Cancel reservation
function cancelReservation(reservationId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الحجز؟')) {
        const reservationIndex = adminState.reservations.findIndex(res => res.id === reservationId);
        if (reservationIndex > -1) {
            adminState.reservations.splice(reservationIndex, 1);
            loadReservations();
            showNotification('تم إلغاء الحجز', 'info');
        }
    }
}

// Generate report
function generateReport() {
    const period = document.getElementById('report-period').value;
    showNotification(`سيتم إنشاء تقرير ${period}`, 'info');
}

// Export customers
function exportCustomers() {
    showNotification('سيتم تصدير بيانات العملاء', 'info');
}

// View customer
function viewCustomer(customerId) {
    showNotification(`عرض تفاصيل العميل ${customerId}`, 'info');
}

// Edit customer
function editCustomer(customerId) {
    showNotification(`تعديل بيانات العميل ${customerId}`, 'info');
}

// Logout
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        window.location.href = 'index.html';
    }
}

// Add CSS for recent orders and popular items
const adminExtraStyles = document.createElement('style');
adminExtraStyles.textContent = `
    .recent-order-item,
    .popular-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--bg-light);
    }
    
    .recent-order-item:last-child,
    .popular-item:last-child {
        border-bottom: none;
    }
    
    .order-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .order-info strong {
        color: var(--text-dark);
        font-size: 0.9rem;
    }
    
    .order-info span {
        color: var(--text-light);
        font-size: 0.8rem;
    }
    
    .order-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .order-total {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 0.9rem;
    }
    
    .item-orders {
        background: var(--bg-light);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        color: var(--text-dark);
    }
    
    .reservation-notes {
        margin: 1rem 0;
        padding: 0.5rem;
        background: var(--bg-light);
        border-radius: var(--border-radius);
        font-size: 0.9rem;
        color: var(--text-light);
    }
    
    .order-customer {
        margin: 0.5rem 0;
    }
    
    .order-customer strong {
        display: block;
        color: var(--text-dark);
    }
    
    .order-customer span {
        color: var(--text-light);
        font-size: 0.9rem;
    }
    
    .order-address {
        margin: 0.5rem 0;
        color: var(--text-light);
        font-size: 0.9rem;
    }
    
    .order-address i {
        color: var(--primary-color);
        margin-left: 0.5rem;
    }
`;
document.head.appendChild(adminExtraStyles);
