// Advanced Admin Panel JavaScript for Bazooka Restaurant
// Developed by Eng<PERSON> <PERSON> - 0532969067

// Admin state
let adminState = {
    currentSection: 'dashboard',
    orders: [],
    reservations: [],
    customers: [],
    menuItems: [],
    stats: {
        totalOrders: 0,
        totalRevenue: 0,
        totalCustomers: 0,
        avgRating: 4.8
    },
    realTimeUpdates: true,
    notifications: [],
    activities: []
};

// Sample data
const sampleOrders = [
    {
        id: 'BZ001234',
        customerName: 'أحمد محمد',
        phone: '+966501234567',
        items: ['مشاوي مشكلة', 'حمص بالطحينة', 'عصير برتقال'],
        total: 125.50,
        status: 'pending',
        time: '2024-01-15 14:30',
        address: 'حي النخيل، الرياض'
    },
    {
        id: 'BZ001235',
        customerName: 'فاطمة علي',
        phone: '+966507654321',
        items: ['برياني لحم', 'سلطة فتوش'],
        total: 89.00,
        status: 'preparing',
        time: '2024-01-15 14:45',
        address: 'حي الملز، الرياض'
    },
    {
        id: 'BZ001236',
        customerName: 'محمد السعد',
        phone: '+966512345678',
        items: ['فروج مشوي', 'كنافة نابلسية', 'شاي أحمر'],
        total: 98.00,
        status: 'ready',
        time: '2024-01-15 15:00',
        address: 'حي العليا، الرياض'
    }
];

const sampleReservations = [
    {
        id: 'RES001',
        name: 'سارة أحمد',
        phone: '+966501111111',
        email: '<EMAIL>',
        date: '2024-01-16',
        time: '19:00',
        guests: 4,
        notes: 'طاولة بجانب النافذة'
    },
    {
        id: 'RES002',
        name: 'خالد محمد',
        phone: '+966502222222',
        email: '<EMAIL>',
        date: '2024-01-16',
        time: '20:30',
        guests: 2,
        notes: 'ذكرى زواج'
    }
];

const sampleCustomers = [
    {
        id: 1,
        name: 'أحمد محمد',
        phone: '+966501234567',
        email: '<EMAIL>',
        orders: 15,
        totalSpent: 1250.00,
        joinDate: '2023-06-15'
    },
    {
        id: 2,
        name: 'فاطمة علي',
        phone: '+966507654321',
        email: '<EMAIL>',
        orders: 8,
        totalSpent: 680.00,
        joinDate: '2023-08-22'
    }
];

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
    loadSampleData();
    setupAdminEventListeners();
});

// Initialize admin panel
function initializeAdmin() {
    console.log('Admin Panel Initialized');
    showSection('dashboard');
    updateDashboardStats();
}

// Load sample data
function loadSampleData() {
    adminState.orders = sampleOrders;
    adminState.reservations = sampleReservations;
    adminState.customers = sampleCustomers;
    adminState.menuItems = menuData || [];
    
    loadRecentOrders();
    loadPopularItems();
    loadOrders();
    loadReservations();
    loadCustomers();
    loadMenuItems();
}

// Setup event listeners
function setupAdminEventListeners() {
    // Menu filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            filterButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            const category = this.getAttribute('data-category');
            filterMenuItems(category);
        });
    });

    // Order filter
    const orderFilter = document.getElementById('order-filter');
    if (orderFilter) {
        orderFilter.addEventListener('change', function() {
            filterOrders(this.value);
        });
    }

    // Customer search
    const customerSearch = document.getElementById('customer-search');
    if (customerSearch) {
        customerSearch.addEventListener('input', function() {
            searchCustomers(this.value);
        });
    }

    // Add item form
    const addItemForm = document.getElementById('add-item-form');
    if (addItemForm) {
        addItemForm.addEventListener('submit', handleAddItem);
    }
}

// Show section
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from menu items
    document.querySelectorAll('.admin-menu li').forEach(item => {
        item.classList.remove('active');
    });
    
    // Show selected section
    const section = document.getElementById(sectionId);
    if (section) {
        section.classList.add('active');
    }
    
    // Add active class to menu item
    const menuItem = document.querySelector(`a[href="#${sectionId}"]`);
    if (menuItem) {
        menuItem.closest('li').classList.add('active');
    }
    
    adminState.currentSection = sectionId;
}

// Update dashboard stats
function updateDashboardStats() {
    const today = new Date().toISOString().split('T')[0];
    const todayOrders = adminState.orders.filter(order => 
        order.time.startsWith(today)
    );
    
    const todaySales = todayOrders.reduce((sum, order) => sum + order.total, 0);
    const newCustomers = adminState.customers.filter(customer => 
        customer.joinDate === today
    ).length;
    
    // Update stat cards
    document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = todayOrders.length;
    document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = `${todaySales.toFixed(2)} ريال`;
    document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = newCustomers;
}

// Load recent orders for dashboard
function loadRecentOrders() {
    const recentOrdersContainer = document.getElementById('recent-orders');
    if (!recentOrdersContainer) return;
    
    const recentOrders = adminState.orders.slice(0, 5);
    
    recentOrdersContainer.innerHTML = recentOrders.map(order => `
        <div class="recent-order-item">
            <div class="order-info">
                <strong>${order.id}</strong>
                <span>${order.customerName}</span>
            </div>
            <div class="order-status ${order.status}">
                ${getStatusText(order.status)}
            </div>
            <div class="order-total">
                ${order.total.toFixed(2)} ريال
            </div>
        </div>
    `).join('');
}

// Load popular items for dashboard
function loadPopularItems() {
    const popularItemsContainer = document.getElementById('popular-items');
    if (!popularItemsContainer) return;
    
    const popularItems = [
        { name: 'مشاوي مشكلة', orders: 45 },
        { name: 'برياني لحم', orders: 32 },
        { name: 'فروج مشوي', orders: 28 },
        { name: 'كنافة نابلسية', orders: 24 },
        { name: 'حمص بالطحينة', orders: 19 }
    ];
    
    popularItemsContainer.innerHTML = popularItems.map(item => `
        <div class="popular-item">
            <span class="item-name">${item.name}</span>
            <span class="item-orders">${item.orders} طلب</span>
        </div>
    `).join('');
}

// Load orders
function loadOrders(filter = 'all') {
    const ordersGrid = document.getElementById('orders-grid');
    if (!ordersGrid) return;
    
    let filteredOrders = adminState.orders;
    if (filter !== 'all') {
        filteredOrders = adminState.orders.filter(order => order.status === filter);
    }
    
    ordersGrid.innerHTML = filteredOrders.map(order => `
        <div class="order-card">
            <div class="order-header">
                <span class="order-number">${order.id}</span>
                <span class="order-status ${order.status}">${getStatusText(order.status)}</span>
            </div>
            <div class="order-customer">
                <strong>${order.customerName}</strong>
                <span>${order.phone}</span>
            </div>
            <div class="order-items">
                ${order.items.join(', ')}
            </div>
            <div class="order-address">
                <i class="fas fa-map-marker-alt"></i> ${order.address}
            </div>
            <div class="order-footer">
                <span class="order-total">${order.total.toFixed(2)} ريال</span>
                <div class="order-actions">
                    ${getOrderActions(order)}
                </div>
            </div>
        </div>
    `).join('');
}

// Get status text in Arabic
function getStatusText(status) {
    const statusMap = {
        'pending': 'قيد الانتظار',
        'preparing': 'قيد التحضير',
        'ready': 'جاهز للتوصيل',
        'delivered': 'تم التوصيل'
    };
    return statusMap[status] || status;
}

// Get order actions based on status
function getOrderActions(order) {
    switch (order.status) {
        case 'pending':
            return `
                <button class="btn-accept" onclick="updateOrderStatus('${order.id}', 'preparing')">قبول</button>
                <button class="btn-reject" onclick="updateOrderStatus('${order.id}', 'cancelled')">رفض</button>
            `;
        case 'preparing':
            return `
                <button class="btn-complete" onclick="updateOrderStatus('${order.id}', 'ready')">جاهز</button>
            `;
        case 'ready':
            return `
                <button class="btn-complete" onclick="updateOrderStatus('${order.id}', 'delivered')">تم التوصيل</button>
            `;
        default:
            return '';
    }
}

// Update order status
function updateOrderStatus(orderId, newStatus) {
    const orderIndex = adminState.orders.findIndex(order => order.id === orderId);
    if (orderIndex > -1) {
        adminState.orders[orderIndex].status = newStatus;
        loadOrders();
        showNotification(`تم تحديث حالة الطلب ${orderId}`, 'success');
    }
}

// Filter orders
function filterOrders(status) {
    loadOrders(status);
}

// Load reservations
function loadReservations() {
    const reservationsGrid = document.getElementById('reservations-grid');
    if (!reservationsGrid) return;
    
    reservationsGrid.innerHTML = adminState.reservations.map(reservation => `
        <div class="reservation-card">
            <div class="reservation-header">
                <span class="reservation-name">${reservation.name}</span>
                <span class="reservation-time">${reservation.date} - ${reservation.time}</span>
            </div>
            <div class="reservation-details">
                <div class="reservation-detail">
                    <i class="fas fa-phone"></i>
                    <span>${reservation.phone}</span>
                </div>
                <div class="reservation-detail">
                    <i class="fas fa-envelope"></i>
                    <span>${reservation.email}</span>
                </div>
                <div class="reservation-detail">
                    <i class="fas fa-users"></i>
                    <span>${reservation.guests} أشخاص</span>
                </div>
            </div>
            ${reservation.notes ? `<div class="reservation-notes">ملاحظات: ${reservation.notes}</div>` : ''}
            <div class="reservation-actions">
                <button class="btn btn-primary btn-sm" onclick="confirmReservation('${reservation.id}')">تأكيد</button>
                <button class="btn btn-secondary btn-sm" onclick="cancelReservation('${reservation.id}')">إلغاء</button>
            </div>
        </div>
    `).join('');
}

// Load customers
function loadCustomers() {
    const customersTable = document.getElementById('customers-tbody');
    if (!customersTable) return;
    
    customersTable.innerHTML = adminState.customers.map(customer => `
        <tr>
            <td>${customer.name}</td>
            <td>${customer.phone}</td>
            <td>${customer.email}</td>
            <td>${customer.orders}</td>
            <td>${customer.totalSpent.toFixed(2)} ريال</td>
            <td>${customer.joinDate}</td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="viewCustomer(${customer.id})">عرض</button>
                <button class="btn btn-secondary btn-sm" onclick="editCustomer(${customer.id})">تعديل</button>
            </td>
        </tr>
    `).join('');
}

// Load menu items for admin
function loadMenuItems(category = 'all') {
    const menuGrid = document.getElementById('admin-menu-grid');
    if (!menuGrid) return;
    
    let filteredItems = adminState.menuItems;
    if (category !== 'all') {
        filteredItems = adminState.menuItems.filter(item => item.category === category);
    }
    
    menuGrid.innerHTML = filteredItems.map(item => `
        <div class="admin-menu-item">
            <img src="${item.image}" alt="${item.name}" loading="lazy">
            <div class="admin-menu-item-content">
                <h3>${item.name}</h3>
                <p>${item.description}</p>
                <div class="admin-menu-item-footer">
                    <span class="price">${item.price.toFixed(2)} ريال</span>
                    <div class="admin-menu-item-actions">
                        <button class="btn-edit" onclick="editMenuItem(${item.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-delete" onclick="deleteMenuItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn-toggle" onclick="toggleMenuItem(${item.id})">
                            <i class="fas fa-eye${item.available === false ? '-slash' : ''}"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Filter menu items
function filterMenuItems(category) {
    loadMenuItems(category);
}

// Show add item modal
function showAddItemModal() {
    const modal = document.getElementById('add-item-modal');
    if (modal) {
        modal.classList.add('show');
    }
}

// Close add item modal
function closeAddItemModal() {
    const modal = document.getElementById('add-item-modal');
    if (modal) {
        modal.classList.remove('show');
    }
}

// Handle add item form
function handleAddItem(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const newItem = {
        id: Date.now(),
        name: formData.get('name'),
        description: formData.get('description'),
        price: parseFloat(formData.get('price')),
        category: formData.get('category'),
        image: formData.get('image'),
        popular: formData.get('popular') === 'on',
        available: true
    };
    
    adminState.menuItems.push(newItem);
    loadMenuItems();
    closeAddItemModal();
    e.target.reset();
    
    showNotification('تم إضافة الطبق بنجاح', 'success');
}

// Edit menu item
function editMenuItem(itemId) {
    const item = adminState.menuItems.find(item => item.id === itemId);
    if (item) {
        // Show edit modal with item data
        showNotification('سيتم إضافة نافذة التعديل قريباً', 'info');
    }
}

// Delete menu item
function deleteMenuItem(itemId) {
    if (confirm('هل أنت متأكد من حذف هذا الطبق؟')) {
        const itemIndex = adminState.menuItems.findIndex(item => item.id === itemId);
        if (itemIndex > -1) {
            adminState.menuItems.splice(itemIndex, 1);
            loadMenuItems();
            showNotification('تم حذف الطبق بنجاح', 'success');
        }
    }
}

// Toggle menu item availability
function toggleMenuItem(itemId) {
    const item = adminState.menuItems.find(item => item.id === itemId);
    if (item) {
        item.available = !item.available;
        loadMenuItems();
        showNotification(`تم ${item.available ? 'تفعيل' : 'إلغاء'} الطبق`, 'success');
    }
}

// Search customers
function searchCustomers(query) {
    const filteredCustomers = adminState.customers.filter(customer =>
        customer.name.toLowerCase().includes(query.toLowerCase()) ||
        customer.phone.includes(query) ||
        customer.email.toLowerCase().includes(query.toLowerCase())
    );
    
    const customersTable = document.getElementById('customers-tbody');
    if (customersTable) {
        customersTable.innerHTML = filteredCustomers.map(customer => `
            <tr>
                <td>${customer.name}</td>
                <td>${customer.phone}</td>
                <td>${customer.email}</td>
                <td>${customer.orders}</td>
                <td>${customer.totalSpent.toFixed(2)} ريال</td>
                <td>${customer.joinDate}</td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="viewCustomer(${customer.id})">عرض</button>
                    <button class="btn btn-secondary btn-sm" onclick="editCustomer(${customer.id})">تعديل</button>
                </td>
            </tr>
        `).join('');
    }
}

// Refresh orders
function refreshOrders() {
    loadOrders();
    showNotification('تم تحديث الطلبات', 'success');
}

// Confirm reservation
function confirmReservation(reservationId) {
    showNotification(`تم تأكيد الحجز ${reservationId}`, 'success');
}

// Cancel reservation
function cancelReservation(reservationId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الحجز؟')) {
        const reservationIndex = adminState.reservations.findIndex(res => res.id === reservationId);
        if (reservationIndex > -1) {
            adminState.reservations.splice(reservationIndex, 1);
            loadReservations();
            showNotification('تم إلغاء الحجز', 'info');
        }
    }
}

// Generate report
function generateReport() {
    const period = document.getElementById('report-period').value;
    showNotification(`سيتم إنشاء تقرير ${period}`, 'info');
}

// Export customers
function exportCustomers() {
    showNotification('سيتم تصدير بيانات العملاء', 'info');
}

// View customer
function viewCustomer(customerId) {
    showNotification(`عرض تفاصيل العميل ${customerId}`, 'info');
}

// Edit customer
function editCustomer(customerId) {
    showNotification(`تعديل بيانات العميل ${customerId}`, 'info');
}

// Logout
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        window.location.href = 'index.html';
    }
}

// Add CSS for recent orders and popular items
const adminExtraStyles = document.createElement('style');
adminExtraStyles.textContent = `
    .recent-order-item,
    .popular-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--bg-light);
    }
    
    .recent-order-item:last-child,
    .popular-item:last-child {
        border-bottom: none;
    }
    
    .order-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .order-info strong {
        color: var(--text-dark);
        font-size: 0.9rem;
    }
    
    .order-info span {
        color: var(--text-light);
        font-size: 0.8rem;
    }
    
    .order-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .order-total {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 0.9rem;
    }
    
    .item-orders {
        background: var(--bg-light);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        color: var(--text-dark);
    }
    
    .reservation-notes {
        margin: 1rem 0;
        padding: 0.5rem;
        background: var(--bg-light);
        border-radius: var(--border-radius);
        font-size: 0.9rem;
        color: var(--text-light);
    }
    
    .order-customer {
        margin: 0.5rem 0;
    }
    
    .order-customer strong {
        display: block;
        color: var(--text-dark);
    }
    
    .order-customer span {
        color: var(--text-light);
        font-size: 0.9rem;
    }
    
    .order-address {
        margin: 0.5rem 0;
        color: var(--text-light);
        font-size: 0.9rem;
    }
    
    .order-address i {
        color: var(--primary-color);
        margin-left: 0.5rem;
    }
`;
document.head.appendChild(adminExtraStyles);

// Advanced Interactive Features by Eng. Mohammed Al-Ashrafi

// Check admin authentication
function checkAdminAuth() {
    const adminSession = localStorage.getItem('adminSession');

    if (!adminSession) {
        window.location.href = 'admin-login.html';
        return;
    }

    try {
        const session = JSON.parse(adminSession);
        const now = new Date().getTime();
        const sessionAge = now - session.timestamp;
        const maxAge = session.remember ? 30 * 24 * 60 * 60 * 1000 : 8 * 60 * 60 * 1000;

        if (sessionAge > maxAge) {
            localStorage.removeItem('adminSession');
            showNotification('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning');
            setTimeout(() => {
                window.location.href = 'admin-login.html';
            }, 2000);
        }
    } catch (error) {
        console.error('Error checking auth:', error);
        window.location.href = 'admin-login.html';
    }
}

// Start real-time updates
function startRealTimeUpdates() {
    console.log('🔄 Starting real-time updates...');

    // Update every 30 seconds
    setInterval(() => {
        updateRealTimeData();
    }, 30000);

    // Simulate new orders
    setInterval(() => {
        simulateNewOrder();
    }, 45000);
}

// Update real-time data
function updateRealTimeData() {
    // Simulate random changes
    adminState.stats.totalOrders += Math.floor(Math.random() * 3);
    adminState.stats.totalRevenue += Math.floor(Math.random() * 500);
    adminState.stats.totalCustomers += Math.floor(Math.random() * 2);

    // Update UI
    updateStats();

    // Add activity
    addActivity('system', 'تحديث البيانات', 'تم تحديث الإحصائيات تلقائياً', 'fas fa-sync-alt', '#007bff');
}

// Simulate new order
function simulateNewOrder() {
    const newOrder = {
        id: 'BZ' + Date.now().toString().slice(-6),
        customerName: 'عميل جديد',
        phone: '+966501234567',
        items: ['طبق جديد'],
        total: Math.floor(Math.random() * 200) + 50,
        status: 'pending',
        time: new Date().toLocaleTimeString('ar-SA'),
        date: new Date().toLocaleDateString('ar-SA')
    };

    adminState.orders.unshift(newOrder);
    loadOrders();

    // Show notification
    showNotification(`طلب جديد: ${newOrder.id}`, 'success');

    // Add activity
    addActivity('order', 'طلب جديد', `طلب جديد من ${newOrder.customerName}`, 'fas fa-shopping-cart', '#28a745');
}

// Initialize interactive features
function initializeInteractiveFeatures() {
    console.log('🎯 Initializing interactive features...');

    // Add drag and drop for orders
    initializeDragAndDrop();

    // Add keyboard shortcuts
    setupKeyboardShortcuts();

    // Add auto-save for forms
    setupAutoSave();

    // Add search functionality
    setupAdvancedSearch();
}

// Initialize drag and drop
function initializeDragAndDrop() {
    // This would implement drag and drop for order management
    console.log('📋 Drag and drop initialized');
}

// Setup keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + D for Dashboard
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            showSection('dashboard');
        }

        // Ctrl/Cmd + O for Orders
        if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
            e.preventDefault();
            showSection('orders');
        }

        // Ctrl/Cmd + M for Menu
        if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
            e.preventDefault();
            showSection('menu');
        }

        // Ctrl/Cmd + R for Reservations
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            showSection('reservations');
        }

        // Ctrl/Cmd + C for Customers
        if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
            e.preventDefault();
            showSection('customers');
        }
    });

    console.log('⌨️ Keyboard shortcuts activated');
}

// Setup auto-save
function setupAutoSave() {
    // Auto-save form data every 30 seconds
    setInterval(() => {
        saveFormData();
    }, 30000);

    console.log('💾 Auto-save enabled');
}

// Save form data
function saveFormData() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        localStorage.setItem(`form_${form.id}`, JSON.stringify(data));
    });
}

// Setup advanced search
function setupAdvancedSearch() {
    // Global search functionality
    const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="بحث"]');

    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            performAdvancedSearch(this.value, this.dataset.searchType);
        });
    });

    console.log('🔍 Advanced search enabled');
}

// Perform advanced search
function performAdvancedSearch(query, type) {
    if (query.length < 2) return;

    switch (type) {
        case 'orders':
            searchOrders(query);
            break;
        case 'customers':
            searchCustomers(query);
            break;
        case 'menu':
            searchMenuItems(query);
            break;
        default:
            globalSearch(query);
    }
}

// Global search
function globalSearch(query) {
    const results = [];

    // Search in orders
    adminState.orders.forEach(order => {
        if (order.customerName.includes(query) || order.id.includes(query)) {
            results.push({type: 'order', data: order});
        }
    });

    // Search in customers
    adminState.customers.forEach(customer => {
        if (customer.name.includes(query) || customer.phone.includes(query)) {
            results.push({type: 'customer', data: customer});
        }
    });

    // Display results
    displaySearchResults(results);
}

// Display search results
function displaySearchResults(results) {
    // This would display search results in a dropdown or modal
    console.log('Search results:', results);
}

// Load notifications
function loadNotifications() {
    adminState.notifications = [
        {
            id: 1,
            title: 'طلب جديد',
            message: 'طلب جديد من أحمد محمد',
            time: 'منذ دقيقتين',
            type: 'order',
            unread: true
        },
        {
            id: 2,
            title: 'حجز جديد',
            message: 'حجز طاولة لـ 4 أشخاص',
            time: 'منذ 5 دقائق',
            type: 'reservation',
            unread: true
        }
    ];

    updateNotificationBadge();
}

// Update notification badge
function updateNotificationBadge() {
    const unreadCount = adminState.notifications.filter(n => n.unread).length;
    const badge = document.querySelector('.notification-badge');

    if (badge) {
        badge.textContent = unreadCount;
        badge.style.display = unreadCount > 0 ? 'block' : 'none';
    }
}

// Add activity
function addActivity(type, title, description, icon, color) {
    const activity = {
        id: Date.now(),
        type,
        title,
        description,
        icon,
        color,
        time: new Date().toLocaleTimeString('ar-SA')
    };

    adminState.activities.unshift(activity);

    // Keep only last 50 activities
    if (adminState.activities.length > 50) {
        adminState.activities = adminState.activities.slice(0, 50);
    }

    updateActivityFeed();
}

// Update activity feed
function updateActivityFeed() {
    const activityFeed = document.getElementById('activity-feed');
    if (!activityFeed) return;

    activityFeed.innerHTML = adminState.activities.slice(0, 10).map(activity => `
        <div class="activity-item">
            <div class="activity-icon" style="background: ${activity.color}">
                <i class="${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${activity.title}</div>
                <div class="activity-description">${activity.description}</div>
                <div class="activity-time">${activity.time}</div>
            </div>
        </div>
    `).join('');
}

// Enhanced notification system
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Auto remove
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.parentElement.removeChild(notification);
                }
            }, 300);
        }
    }, duration);
}

// Get notification icon
function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Advanced Admin Panel Initialized by Eng. Mohammed Al-Ashrafi - 0532969067');

    // Check admin authentication
    checkAdminAuth();

    // Load sample data
    loadSampleData();

    // Show dashboard by default
    showSection('dashboard');

    // Update statistics
    updateStats();

    // Start real-time updates
    if (adminState.realTimeUpdates) {
        startRealTimeUpdates();
    }

    // Initialize interactive features
    initializeInteractiveFeatures();

    // Load notifications
    loadNotifications();

    // Add welcome message
    setTimeout(() => {
        showNotification('مرحباً بك في لوحة الإدارة المتقدمة!', 'success');
        addActivity('system', 'تسجيل دخول', 'تم تسجيل دخول المدير بنجاح', 'fas fa-sign-in-alt', '#28a745');
    }, 1000);
});
