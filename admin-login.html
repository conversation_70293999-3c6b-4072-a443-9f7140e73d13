<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول الإدارة - مطعم بازوكا</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/auth.css">
    <link rel="stylesheet" href="styles/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Cairo', sans-serif;
        }
        
        .admin-login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
            margin: 2rem;
        }
        
        .admin-login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .admin-logo {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .admin-login-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .admin-login-header p {
            color: var(--text-light);
            font-size: 1rem;
        }
        
        .security-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .back-to-site {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: var(--transition);
            backdrop-filter: blur(10px);
        }
        
        .back-to-site:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .admin-features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        
        .admin-features h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }
        
        .feature-list {
            display: grid;
            gap: 0.75rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.9rem;
            color: var(--text-light);
        }
        
        .feature-item i {
            color: var(--primary-color);
            width: 16px;
        }
        
        @media (max-width: 768px) {
            .admin-login-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .back-to-site {
                position: static;
                display: block;
                text-align: center;
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-to-site">
        <i class="fas fa-arrow-right"></i> العودة للموقع
    </a>

    <div class="admin-login-container">
        <div class="admin-login-header">
            <div class="admin-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h1>لوحة إدارة بازوكا</h1>
            <p>تسجيل دخول آمن للمدراء</p>
        </div>

        <div class="security-badge">
            <i class="fas fa-lock"></i>
            <span>اتصال آمن ومشفر</span>
        </div>

        <form id="admin-login-form" class="auth-form">
            <div class="form-group">
                <label for="admin-email">البريد الإلكتروني الإداري</label>
                <div class="input-group">
                    <i class="fas fa-user-shield"></i>
                    <input type="email" id="admin-email" name="email" placeholder="<EMAIL>" required>
                </div>
            </div>

            <div class="form-group">
                <label for="admin-password">كلمة المرور</label>
                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="admin-password" name="password" placeholder="أدخل كلمة المرور" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('admin-password')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="admin-2fa">رمز التحقق بخطوتين (اختياري)</label>
                <div class="input-group">
                    <i class="fas fa-mobile-alt"></i>
                    <input type="text" id="admin-2fa" name="twoFactor" placeholder="123456" maxlength="6">
                </div>
                <small class="form-help">أدخل الرمز من تطبيق المصادقة إذا كان مفعلاً</small>
            </div>

            <div class="form-options">
                <label class="checkbox-label">
                    <input type="checkbox" id="admin-remember" name="remember">
                    <span class="checkmark"></span>
                    تذكر هذا الجهاز لمدة 30 يوماً
                </label>
            </div>

            <button type="submit" class="btn btn-primary auth-btn">
                <span class="btn-text">
                    <i class="fas fa-sign-in-alt"></i> دخول لوحة الإدارة
                </span>
                <div class="btn-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    جاري التحقق من الهوية...
                </div>
            </button>

            <div class="auth-switch">
                <p>
                    <a href="#" onclick="showForgotAdminPassword()">
                        <i class="fas fa-key"></i> نسيت كلمة المرور؟
                    </a>
                </p>
            </div>
        </form>

        <div class="admin-features">
            <h3>مميزات لوحة الإدارة</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <i class="fas fa-chart-line"></i>
                    <span>تقارير مبيعات مفصلة</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-users"></i>
                    <span>إدارة العملاء والطلبات</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-utensils"></i>
                    <span>إدارة قائمة الطعام</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-calendar"></i>
                    <span>إدارة الحجوزات</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-cog"></i>
                    <span>إعدادات النظام</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="modal auth-modal" id="forgot-admin-password-modal">
        <div class="modal-content auth-modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-key"></i> استعادة كلمة مرور الإدارة</h3>
                <button class="modal-close" onclick="closeForgotAdminPassword()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="security-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>لأسباب أمنية، سيتم إرسال رابط الاستعادة إلى البريد الإلكتروني المسجل مع إشعار إضافي للمدير العام.</p>
                </div>
                <form id="forgot-admin-password-form" class="auth-form">
                    <div class="form-group">
                        <label for="forgot-admin-email">البريد الإلكتروني الإداري</label>
                        <div class="input-group">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="forgot-admin-email" name="email" placeholder="<EMAIL>" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="admin-id">رقم الهوية الإدارية</label>
                        <div class="input-group">
                            <i class="fas fa-id-card"></i>
                            <input type="text" id="admin-id" name="adminId" placeholder="ADM001" required>
                        </div>
                        <small class="form-help">أدخل رقم الهوية الإدارية للتحقق من الهوية</small>
                    </div>
                    <button type="submit" class="btn btn-primary auth-btn">
                        <span class="btn-text">إرسال رابط الاستعادة</span>
                        <div class="btn-loading" style="display: none;">
                            <div class="loading-spinner"></div>
                            جاري المعالجة...
                        </div>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="scripts/main.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        // Admin login specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            setupAdminLogin();
        });

        function setupAdminLogin() {
            const adminLoginForm = document.getElementById('admin-login-form');
            const forgotAdminForm = document.getElementById('forgot-admin-password-form');

            if (adminLoginForm) {
                adminLoginForm.addEventListener('submit', handleAdminLogin);
            }

            if (forgotAdminForm) {
                forgotAdminForm.addEventListener('submit', handleForgotAdminPassword);
            }

            // Auto-fill demo credentials
            document.getElementById('admin-email').value = '<EMAIL>';
            document.getElementById('admin-password').value = 'Admin@123';
        }

        async function handleAdminLogin(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const email = formData.get('email');
            const password = formData.get('password');
            const twoFactor = formData.get('twoFactor');
            const remember = formData.get('remember');

            const submitBtn = e.target.querySelector('button[type="submit"]');
            showButtonLoading(submitBtn);

            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Check credentials (in real app, this would be server-side)
                if (email === '<EMAIL>' && password === 'Admin@123') {
                    // Simulate 2FA check if provided
                    if (twoFactor && twoFactor !== '123456') {
                        throw new Error('رمز التحقق بخطوتين غير صحيح');
                    }

                    // Login successful
                    localStorage.setItem('adminSession', JSON.stringify({
                        email: email,
                        timestamp: new Date().getTime(),
                        remember: remember === 'on'
                    }));

                    showNotification('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = 'admin.html';
                    }, 1500);

                } else {
                    throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة');
                }

            } catch (error) {
                showNotification(error.message, 'error');
            } finally {
                hideButtonLoading(submitBtn);
            }
        }

        async function handleForgotAdminPassword(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const email = formData.get('email');
            const adminId = formData.get('adminId');

            const submitBtn = e.target.querySelector('button[type="submit"]');
            showButtonLoading(submitBtn);

            try {
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1500));

                closeForgotAdminPassword();
                showNotification('تم إرسال رابط استعادة كلمة المرور. تحقق من بريدك الإلكتروني.', 'success');

            } catch (error) {
                showNotification(error.message, 'error');
            } finally {
                hideButtonLoading(submitBtn);
            }
        }

        function showForgotAdminPassword() {
            const modal = document.getElementById('forgot-admin-password-modal');
            if (modal) {
                modal.classList.add('show');
            }
        }

        function closeForgotAdminPassword() {
            const modal = document.getElementById('forgot-admin-password-modal');
            if (modal) {
                modal.classList.remove('show');
            }
        }

        // Add security warning styles
        const securityStyles = document.createElement('style');
        securityStyles.textContent = `
            .security-warning {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: var(--border-radius);
                padding: 1rem;
                margin-bottom: 1.5rem;
                display: flex;
                align-items: flex-start;
                gap: 0.75rem;
            }
            
            .security-warning i {
                color: #856404;
                font-size: 1.2rem;
                margin-top: 0.1rem;
            }
            
            .security-warning p {
                color: #856404;
                font-size: 0.9rem;
                line-height: 1.5;
                margin: 0;
            }
        `;
        document.head.appendChild(securityStyles);
    </script>
</body>
</html>
