// Main JavaScript file for Bazooka Restaurant

// DOM Elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const cartSidebar = document.getElementById('cart-sidebar');
const cartCount = document.querySelector('.cart-count');
const cartTotal = document.getElementById('cart-total');
const contactForm = document.getElementById('contact-form');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadHeroImages();
    setupEventListeners();
    setupScrollAnimations();
});

// Initialize application
function initializeApp() {
    console.log('Bazooka Restaurant System Initialized');
    updateCartDisplay();
    
    // Add smooth scrolling to navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', toggleMobileMenu);
    }

    // Contact form submission
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }

    // Close cart when clicking outside
    document.addEventListener('click', function(e) {
        if (cartSidebar && cartSidebar.classList.contains('open') && 
            !cartSidebar.contains(e.target) && 
            !e.target.closest('.cart-btn')) {
            toggleCart();
        }
    });

    // Navbar scroll effect
    window.addEventListener('scroll', handleNavbarScroll);
}

// Toggle mobile menu
function toggleMobileMenu() {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
    
    // Animate hamburger
    const spans = hamburger.querySelectorAll('span');
    if (hamburger.classList.contains('active')) {
        spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
        spans[1].style.opacity = '0';
        spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
    } else {
        spans[0].style.transform = 'none';
        spans[1].style.opacity = '1';
        spans[2].style.transform = 'none';
    }
}

// Handle navbar scroll effect
function handleNavbarScroll() {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    } else {
        header.style.background = '#ffffff';
        header.style.backdropFilter = 'none';
    }
}

// Toggle cart sidebar
function toggleCart() {
    if (cartSidebar) {
        cartSidebar.classList.toggle('open');
        
        // Add overlay
        if (cartSidebar.classList.contains('open')) {
            const overlay = document.createElement('div');
            overlay.className = 'cart-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 1000;
            `;
            overlay.addEventListener('click', toggleCart);
            document.body.appendChild(overlay);
            document.body.style.overflow = 'hidden';
        } else {
            const overlay = document.querySelector('.cart-overlay');
            if (overlay) {
                overlay.remove();
            }
            document.body.style.overflow = '';
        }
    }
}

// Scroll to section
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Handle contact form submission
function handleContactForm(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData);
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<div class="loading"></div> جاري الإرسال...';
    submitBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
        showNotification('تم إرسال طلب الحجز بنجاح! سنتواصل معك قريباً.', 'success');
        e.target.reset();
        
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Hide notification after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Load hero images with rotation
function loadHeroImages() {
    const heroImg = document.getElementById('hero-img');
    if (!heroImg) return;
    
    const images = [
        'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ];
    
    let currentIndex = 0;
    
    // Set initial image
    heroImg.src = images[currentIndex];
    heroImg.alt = 'طعام شهي من مطعم بازوكا';
    
    // Rotate images every 5 seconds
    setInterval(() => {
        currentIndex = (currentIndex + 1) % images.length;
        heroImg.style.opacity = '0';
        
        setTimeout(() => {
            heroImg.src = images[currentIndex];
            heroImg.style.opacity = '1';
        }, 300);
    }, 5000);
}

// Setup scroll animations
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .menu-item, .about-text, .contact-item');
    animateElements.forEach(el => observer.observe(el));
}

// Update cart display
function updateCartDisplay() {
    const cart = getCart();
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    if (cartCount) {
        cartCount.textContent = totalItems;
    }
    
    if (cartTotal) {
        cartTotal.textContent = totalPrice.toFixed(2);
    }
    
    renderCartItems();
}

// Get cart from localStorage
function getCart() {
    return JSON.parse(localStorage.getItem('bazookaCart') || '[]');
}

// Save cart to localStorage
function saveCart(cart) {
    localStorage.setItem('bazookaCart', JSON.stringify(cart));
    updateCartDisplay();
}

// Render cart items
function renderCartItems() {
    const cartItemsContainer = document.getElementById('cart-items');
    if (!cartItemsContainer) return;
    
    const cart = getCart();
    
    if (cart.length === 0) {
        cartItemsContainer.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--text-light);">
                <i class="fas fa-shopping-cart" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>السلة فارغة</p>
                <p>أضف بعض الأطباق الشهية!</p>
            </div>
        `;
        return;
    }
    
    cartItemsContainer.innerHTML = cart.map(item => `
        <div class="cart-item">
            <img src="${item.image}" alt="${item.name}">
            <div class="cart-item-info">
                <h4>${item.name}</h4>
                <div class="cart-item-controls">
                    <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                    <span style="margin: 0 10px; font-weight: 600;">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                    <span style="margin-right: 10px; color: var(--primary-color); font-weight: 600;">
                        ${(item.price * item.quantity).toFixed(2)} ريال
                    </span>
                </div>
            </div>
        </div>
    `).join('');
}

// Update item quantity in cart
function updateQuantity(itemId, change) {
    const cart = getCart();
    const itemIndex = cart.findIndex(item => item.id === itemId);
    
    if (itemIndex > -1) {
        cart[itemIndex].quantity += change;
        
        if (cart[itemIndex].quantity <= 0) {
            cart.splice(itemIndex, 1);
            showNotification('تم حذف العنصر من السلة', 'info');
        } else {
            showNotification('تم تحديث الكمية', 'success');
        }
        
        saveCart(cart);
    }
}

// Checkout function
function checkout() {
    const cart = getCart();
    
    if (cart.length === 0) {
        showNotification('السلة فارغة! أضف بعض الأطباق أولاً.', 'error');
        return;
    }
    
    // Create order summary
    const orderSummary = cart.map(item => 
        `${item.name} x${item.quantity} = ${(item.price * item.quantity).toFixed(2)} ريال`
    ).join('\n');
    
    const totalPrice = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    // Show order modal
    showOrderModal(orderSummary, totalPrice);
}

// Show order modal
function showOrderModal(orderSummary, totalPrice) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>تأكيد الطلب</h3>
                <button class="modal-close" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body">
                <h4>ملخص الطلب:</h4>
                <pre style="white-space: pre-wrap; font-family: inherit; margin: 1rem 0;">${orderSummary}</pre>
                <hr>
                <p style="font-size: 1.2rem; font-weight: 600; color: var(--primary-color);">
                    المجموع الكلي: ${totalPrice.toFixed(2)} ريال
                </p>
                <form id="order-form" style="margin-top: 1.5rem;">
                    <input type="text" placeholder="الاسم الكامل" required style="width: 100%; margin-bottom: 1rem; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <input type="tel" placeholder="رقم الهاتف" required style="width: 100%; margin-bottom: 1rem; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    <textarea placeholder="العنوان" required style="width: 100%; margin-bottom: 1rem; padding: 10px; border: 1px solid #ddd; border-radius: 5px; height: 80px;"></textarea>
                    <button type="submit" class="btn btn-primary" style="width: 100%;">تأكيد الطلب</button>
                </form>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Show modal
    setTimeout(() => {
        modal.classList.add('show');
    }, 100);
    
    // Handle form submission
    const orderForm = modal.querySelector('#order-form');
    orderForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Clear cart
        localStorage.removeItem('bazookaCart');
        updateCartDisplay();
        
        // Close modal and cart
        closeModal(modal.querySelector('.modal-close'));
        toggleCart();
        
        // Show success message
        showNotification('تم تأكيد طلبك بنجاح! سنتواصل معك قريباً.', 'success');
    });
}

// Close modal
function closeModal(closeBtn) {
    const modal = closeBtn.closest('.modal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.remove();
    }, 300);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

// Add loading state to buttons
function addLoadingState(button, text = 'جاري التحميل...') {
    const originalText = button.textContent;
    button.innerHTML = `<div class="loading"></div> ${text}`;
    button.disabled = true;
    
    return () => {
        button.textContent = originalText;
        button.disabled = false;
    };
}
