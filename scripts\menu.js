// Menu management for Bazooka Restaurant

// Menu data
const menuData = [
    // Appetizers
    {
        id: 1,
        name: 'حمص بالطحينة',
        description: 'حمص كريمي مع الطحينة والزيت والصنوبر',
        price: 18.00,
        category: 'appetizers',
        image: 'https://images.unsplash.com/photo-1571197119282-7c4a2b8b8c8a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        popular: true
    },
    {
        id: 2,
        name: 'متبل الباذنجان',
        description: 'باذنجان مشوي مع الطحينة والثوم والليمون',
        price: 16.00,
        category: 'appetizers',
        image: 'https://images.unsplash.com/photo-1599487488170-d11ec9c172f0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 3,
        name: 'فتوش',
        description: 'سلطة خضار مشكلة مع الخبز المحمص والسماق',
        price: 22.00,
        category: 'appetizers',
        image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 4,
        name: 'كبة نية',
        description: 'كبة نيئة مع البرغل واللحم والبصل والتوابل',
        price: 35.00,
        category: 'appetizers',
        image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },

    // Main Dishes
    {
        id: 5,
        name: 'مشاوي مشكلة',
        description: 'تشكيلة من الكباب والشيش طاووق والكفتة',
        price: 85.00,
        category: 'main',
        image: 'https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        popular: true
    },
    {
        id: 6,
        name: 'فروج مشوي',
        description: 'دجاج كامل مشوي مع الأرز والسلطة',
        price: 65.00,
        category: 'main',
        image: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 7,
        name: 'برياني لحم',
        description: 'أرز برياني مع قطع اللحم والتوابل الهندية',
        price: 75.00,
        category: 'main',
        image: 'https://images.unsplash.com/photo-1563379091339-03246963d96c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 8,
        name: 'سمك مشوي',
        description: 'سمك طازج مشوي مع الخضار والأرز',
        price: 70.00,
        category: 'main',
        image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 9,
        name: 'منسف أردني',
        description: 'لحم مع الأرز واللبن الجميد والمكسرات',
        price: 80.00,
        category: 'main',
        image: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        popular: true
    },
    {
        id: 10,
        name: 'بيتزا مارجريتا',
        description: 'بيتزا كلاسيكية مع الطماطم والجبن والريحان',
        price: 45.00,
        category: 'main',
        image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },

    // Desserts
    {
        id: 11,
        name: 'كنافة نابلسية',
        description: 'كنافة طازجة مع الجبن والقطر',
        price: 25.00,
        category: 'desserts',
        image: 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        popular: true
    },
    {
        id: 12,
        name: 'بقلاوة',
        description: 'حلوى شرقية مع الفستق والعسل',
        price: 20.00,
        category: 'desserts',
        image: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 13,
        name: 'مهلبية',
        description: 'حلوى كريمية مع ماء الورد والفستق',
        price: 18.00,
        category: 'desserts',
        image: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 14,
        name: 'تيراميسو',
        description: 'حلوى إيطالية مع القهوة والمسكربون',
        price: 28.00,
        category: 'desserts',
        image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },

    // Drinks
    {
        id: 15,
        name: 'عصير برتقال طازج',
        description: 'عصير برتقال طبيعي 100%',
        price: 12.00,
        category: 'drinks',
        image: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 16,
        name: 'شاي أحمر',
        description: 'شاي أحمر مع النعناع والسكر',
        price: 8.00,
        category: 'drinks',
        image: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 17,
        name: 'قهوة عربية',
        description: 'قهوة عربية أصيلة مع الهيل',
        price: 10.00,
        category: 'drinks',
        image: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 18,
        name: 'عصير ليمون بالنعناع',
        description: 'مشروب منعش مع الليمون والنعناع',
        price: 14.00,
        category: 'drinks',
        image: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        popular: true
    },
    {
        id: 19,
        name: 'كوكتيل فواكه',
        description: 'مزيج من الفواكه الطازجة',
        price: 18.00,
        category: 'drinks',
        image: 'https://images.unsplash.com/photo-1546173159-315724a31696?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
        id: 20,
        name: 'لاسي مانجو',
        description: 'مشروب هندي بالمانجو واللبن',
        price: 16.00,
        category: 'drinks',
        image: 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    }
];

// Initialize menu when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    loadMenu();
    setupMenuFilters();
});

// Load and display menu items
function loadMenu(category = 'all') {
    const menuGrid = document.getElementById('menu-grid');
    if (!menuGrid) return;

    // Filter items based on category
    const filteredItems = category === 'all' 
        ? menuData 
        : menuData.filter(item => item.category === category);

    // Generate HTML for menu items
    menuGrid.innerHTML = filteredItems.map(item => `
        <div class="menu-item" data-category="${item.category}">
            <div class="menu-item-image">
                <img src="${item.image}" alt="${item.name}" loading="lazy">
                ${item.popular ? '<span class="popular-badge">الأكثر طلباً</span>' : ''}
            </div>
            <div class="menu-item-content">
                <h3>${item.name}</h3>
                <p>${item.description}</p>
                <div class="menu-item-footer">
                    <span class="price">${item.price.toFixed(2)} ريال</span>
                    <button class="add-to-cart" onclick="addToCart(${item.id})">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    // Add animation to menu items
    const menuItems = menuGrid.querySelectorAll('.menu-item');
    menuItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Setup menu category filters
function setupMenuFilters() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Get category and load menu
            const category = this.getAttribute('data-category');
            loadMenu(category);
        });
    });
}

// Add item to cart
function addToCart(itemId) {
    const item = menuData.find(item => item.id === itemId);
    if (!item) return;

    const cart = getCart();
    const existingItem = cart.find(cartItem => cartItem.id === itemId);

    if (existingItem) {
        existingItem.quantity += 1;
        showNotification(`تم زيادة كمية ${item.name}`, 'success');
    } else {
        cart.push({
            id: item.id,
            name: item.name,
            price: item.price,
            image: item.image,
            quantity: 1
        });
        showNotification(`تم إضافة ${item.name} إلى السلة`, 'success');
    }

    saveCart(cart);
    
    // Add visual feedback
    const button = event.target.closest('.add-to-cart');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i> تم الإضافة';
    button.style.background = '#27ae60';
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.style.background = '';
    }, 1500);
}

// Search functionality
function searchMenu(query) {
    const menuGrid = document.getElementById('menu-grid');
    if (!menuGrid) return;

    const filteredItems = menuData.filter(item => 
        item.name.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase())
    );

    if (filteredItems.length === 0) {
        menuGrid.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: var(--text-light);">
                <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <h3>لم يتم العثور على نتائج</h3>
                <p>جرب البحث بكلمات أخرى</p>
            </div>
        `;
        return;
    }

    menuGrid.innerHTML = filteredItems.map(item => `
        <div class="menu-item" data-category="${item.category}">
            <div class="menu-item-image">
                <img src="${item.image}" alt="${item.name}" loading="lazy">
                ${item.popular ? '<span class="popular-badge">الأكثر طلباً</span>' : ''}
            </div>
            <div class="menu-item-content">
                <h3>${item.name}</h3>
                <p>${item.description}</p>
                <div class="menu-item-footer">
                    <span class="price">${item.price.toFixed(2)} ريال</span>
                    <button class="add-to-cart" onclick="addToCart(${item.id})">
                        <i class="fas fa-plus"></i> إضافة
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Get popular items
function getPopularItems() {
    return menuData.filter(item => item.popular);
}

// Get items by category
function getItemsByCategory(category) {
    return menuData.filter(item => item.category === category);
}

// Get item by ID
function getItemById(id) {
    return menuData.find(item => item.id === id);
}

// Add CSS for popular badge
const style = document.createElement('style');
style.textContent = `
    .menu-item-image {
        position: relative;
        overflow: hidden;
    }
    
    .popular-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: var(--accent-color);
        color: var(--white);
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        z-index: 2;
    }
    
    .menu-item-image img {
        transition: transform 0.3s ease;
    }
    
    .menu-item:hover .menu-item-image img {
        transform: scale(1.05);
    }
`;
document.head.appendChild(style);
