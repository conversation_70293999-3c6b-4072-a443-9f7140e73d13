# 🔐 دليل الأمان - نظام مطعم بازوكا

## نظرة عامة على الأمان

تم تطوير نظام مطعم بازوكا بأعلى معايير الأمان لحماية بيانات المستخدمين وضمان سلامة النظام.

## 🛡️ مميزات الأمان المتقدمة

### 1. نظام المصادقة متعدد المستويات

#### للعملاء:
- **تسجيل دخول آمن** مع التحقق من صحة البيانات
- **كلمات مرور قوية** مع متطلبات أمان صارمة
- **التحقق من البريد الإلكتروني** عند التسجيل
- **استعادة كلمة المرور** بطريقة آمنة

#### للإدارة:
- **مصادقة إضافية** للوصول للوحة الإدارة
- **التحقق بخطوتين** (اختياري)
- **جلسات محدودة الوقت** مع تحذيرات انتهاء الصلاحية
- **تسجيل دخول منفصل** عن العملاء

### 2. حماية من الهجمات

#### منع محاولات الدخول المتكررة:
- **حد أقصى 5 محاولات** دخول خاطئة
- **قفل الحساب لمدة 15 دقيقة** بعد تجاوز الحد
- **مراقبة الأنشطة المشبوهة** في الوقت الفعلي

#### حماية من هجمات CSRF:
- **رموز حماية** في جميع النماذج
- **التحقق من المصدر** للطلبات

### 3. إدارة الجلسات

#### جلسات العملاء:
- **انتهاء صلاحية تلقائي** بعد 30 دقيقة من عدم النشاط
- **خيار "تذكرني"** لجلسات طويلة المدى
- **تشفير بيانات الجلسة** في التخزين المحلي

#### جلسات الإدارة:
- **انتهاء صلاحية بعد 8 ساعات** (أو 30 يوماً مع "تذكر الجهاز")
- **تحذيرات قبل انتهاء الصلاحية** بـ 30 دقيقة
- **إمكانية تجديد الجلسة** دون إعادة تسجيل دخول

### 4. التحقق بخطوتين (2FA)

#### للمستخدمين المتقدمين:
- **رموز تحقق عبر SMS** (محاكاة في النسخة التجريبية)
- **رموز مكونة من 6 أرقام**
- **انتهاء صلاحية الرموز** بعد 5 دقائق
- **إعادة إرسال الرموز** مع حد زمني

### 5. مراقبة الأمان

#### تسجيل الأحداث الأمنية:
- **محاولات تسجيل الدخول** (ناجحة وفاشلة)
- **تغييرات كلمات المرور**
- **الوصول للصفحات الحساسة**
- **الأنشطة المشبوهة**

#### معلومات الجلسة:
- **عنوان IP** (محاكاة)
- **نوع المتصفح والجهاز**
- **الطابع الزمني** لكل نشاط

## 🔑 متطلبات كلمة المرور

### للعملاء والمدراء:
- **8 أحرف على الأقل**
- **حرف كبير واحد على الأقل** (A-Z)
- **حرف صغير واحد على الأقل** (a-z)
- **رقم واحد على الأقل** (0-9)
- **رمز خاص واحد على الأقل** (!@#$%^&*)

### مؤشر قوة كلمة المرور:
- **ضعيفة**: أقل من 3 معايير
- **متوسطة**: 3 معايير
- **جيدة**: 4 معايير
- **قوية**: جميع المعايير الـ5

## 🚨 إجراءات الطوارئ

### في حالة اختراق محتمل:
1. **قفل جميع الجلسات** فوراً
2. **إجبار تغيير كلمات المرور**
3. **مراجعة سجلات الأمان**
4. **إشعار المستخدمين المتأثرين**

### في حالة نسيان كلمة المرور:
1. **التحقق من الهوية** عبر البريد الإلكتروني
2. **إرسال رابط آمن** لإعادة تعيين كلمة المرور
3. **انتهاء صلاحية الرابط** بعد ساعة واحدة

## 🔧 إعدادات الأمان للمطورين

### متغيرات الأمان في الكود:
```javascript
const authState = {
    maxLoginAttempts: 5,        // الحد الأقصى لمحاولات الدخول
    lockoutTime: 15 * 60 * 1000, // مدة القفل (15 دقيقة)
    sessionTimeout: 30 * 60 * 1000, // انتهاء الجلسة (30 دقيقة)
    adminSessionTimeout: 8 * 60 * 60 * 1000, // جلسة الإدارة (8 ساعات)
    twoFactorCodeExpiry: 5 * 60 * 1000, // انتهاء رمز 2FA (5 دقائق)
    passwordResetExpiry: 60 * 60 * 1000 // انتهاء رابط استعادة كلمة المرور (ساعة)
};
```

### تشفير البيانات:
- **تشفير كلمات المرور** باستخدام bcrypt (في التطبيق الحقيقي)
- **تشفير بيانات الجلسة** في التخزين المحلي
- **حماية البيانات الحساسة** من XSS و CSRF

## 📊 مراقبة الأداء الأمني

### مؤشرات الأمان:
- **معدل محاولات الدخول الفاشلة**
- **عدد الحسابات المقفلة**
- **وقت استجابة نظام المصادقة**
- **استخدام التحقق بخطوتين**

### تقارير الأمان:
- **تقرير يومي** عن الأنشطة المشبوهة
- **تقرير أسبوعي** عن أداء النظام
- **تقرير شهري** عن إحصائيات الأمان

## 🛠️ أفضل الممارسات

### للمستخدمين:
1. **استخدم كلمة مرور قوية وفريدة**
2. **فعّل التحقق بخطوتين** إذا كان متاحاً
3. **لا تشارك معلومات تسجيل الدخول**
4. **سجل خروج** عند الانتهاء من الاستخدام
5. **تحديث المتصفح** بانتظام

### للمدراء:
1. **مراجعة سجلات الأمان** بانتظام
2. **تحديث كلمات المرور** كل 3 أشهر
3. **استخدام أجهزة آمنة** للوصول للوحة الإدارة
4. **عدم الوصول من شبكات عامة**
5. **الإبلاغ عن أي نشاط مشبوه**

## 🔄 تحديثات الأمان

### النسخة الحالية (v1.0):
- ✅ نظام مصادقة أساسي
- ✅ التحقق بخطوتين
- ✅ حماية من محاولات الدخول المتكررة
- ✅ إدارة الجلسات المتقدمة
- ✅ مراقبة الأمان

### التحديثات المستقبلية:
- 🔄 تكامل مع خدمات المصادقة الخارجية
- 🔄 تشفير متقدم للبيانات
- 🔄 مصادقة بيومترية
- 🔄 تحليل سلوك المستخدم
- 🔄 حماية متقدمة من البوتات

## 📞 الإبلاغ عن مشاكل الأمان

إذا اكتشفت أي مشكلة أمنية، يرجى التواصل معنا فوراً:

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 123 4567 (خط الطوارئ)
- **الموقع**: نموذج الإبلاغ عن المشاكل الأمنية

---

**تذكر**: الأمان مسؤولية مشتركة بين النظام والمستخدمين. اتبع أفضل الممارسات لضمان حماية بياناتك.
