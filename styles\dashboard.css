/* Advanced Dashboard Styles for Bazooka Restaurant */

.dashboard-body {
    margin: 0;
    padding: 0;
    background: #f8f9fa;
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation */
.top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: var(--white);
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-dark);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--bg-light);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb-item {
    color: var(--text-light);
}

.breadcrumb-item.active {
    color: var(--text-dark);
    font-weight: 600;
}

.nav-center {
    flex: 1;
    max-width: 500px;
    margin: 0 2rem;
}

.search-container {
    position: relative;
    width: 100%;
}

.search-container i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.search-container input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: var(--transition);
    background: #f8f9fa;
}

.search-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

.nav-right {
    display: flex;
    align-items: center;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.1rem;
    color: var(--text-dark);
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: var(--bg-light);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 8px 12px;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    margin-right: 1rem;
}

.user-profile:hover {
    background: var(--bg-light);
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-weight: 600;
    color: var(--text-dark);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: var(--white);
    border-left: 1px solid #e9ecef;
    overflow-y: auto;
    z-index: 999;
    transition: var(--transition);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.sidebar .logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.sidebar-menu {
    padding: 1rem 0;
}

.menu-section {
    margin-bottom: 2rem;
}

.menu-section h3 {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0 1.5rem;
    margin-bottom: 0.75rem;
}

.menu-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-section li {
    margin-bottom: 0.25rem;
}

.menu-section a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.menu-section a:hover,
.menu-section li.active a {
    background: var(--bg-light);
    color: var(--primary-color);
}

.menu-section li.active a::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
}

.menu-section a i {
    width: 20px;
    text-align: center;
    font-size: 1rem;
}

.menu-section .badge {
    background: var(--primary-color);
    color: var(--white);
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-right: auto;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    background: var(--bg-light);
}

.system-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-light);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
}

.status-dot.online {
    background: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Developer Credit */
.developer-credit {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.developer-info-mini {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, var(--primary-color), #e55a2b);
    border-radius: 10px;
    color: white;
    font-size: 0.8rem;
    transition: var(--transition);
}

.developer-info-mini:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.developer-info-mini i {
    font-size: 1.2rem;
    animation: developerIconRotate 3s linear infinite;
}

.developer-text {
    display: flex;
    flex-direction: column;
    line-height: 1.3;
}

.developer-name-mini {
    font-weight: 600;
    font-size: 0.85rem;
}

.developer-phone-mini {
    font-size: 0.75rem;
    opacity: 0.9;
    direction: ltr;
    text-align: left;
}

@keyframes developerIconRotate {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(5deg); }
    50% { transform: rotate(0deg); }
    75% { transform: rotate(-5deg); }
    100% { transform: rotate(0deg); }
}

/* Main Content */
.main-content {
    margin-right: 280px;
    margin-top: 70px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
    transition: var(--transition);
}

.main-content.expanded {
    margin-right: 70px;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.section-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.stat-card.revenue::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.stat-card.orders::before {
    background: linear-gradient(90deg, #007bff, #6610f2);
}

.stat-card.customers::before {
    background: linear-gradient(90deg, #fd7e14, #e83e8c);
}

.stat-card.rating::before {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.stat-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    background: var(--primary-color);
}

.stat-card.revenue .stat-icon {
    background: #28a745;
}

.stat-card.orders .stat-icon {
    background: #007bff;
}

.stat-card.customers .stat-icon {
    background: #fd7e14;
}

.stat-card.rating .stat-icon {
    background: #ffc107;
}

.stat-content {
    margin-right: 70px;
}

.stat-content h3 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.75rem;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

.stat-change.neutral {
    color: var(--text-light);
}

.stat-change small {
    color: var(--text-light);
    font-weight: 400;
}

.stat-chart {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    opacity: 0.3;
}

.rating-stars {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.rating-stars i {
    color: #ffc107;
    font-size: 1rem;
}

/* Charts */
.charts-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.chart-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.chart-controls select {
    padding: 6px 12px;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    background: var(--white);
}

.chart-content {
    height: 300px;
    position: relative;
}

/* Dashboard Widgets */
.dashboard-widgets {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.widget {
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: var(--bg-light);
}

.widget-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.widget-content {
    padding: 1.5rem;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--white);
    background: var(--primary-color);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.activity-description {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-light);
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: var(--bg-light);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.quick-action:hover {
    border-color: var(--primary-color);
    background: var(--white);
    transform: translateY(-2px);
}

.quick-action i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.quick-action span {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-dark);
}

/* Real-time Section */
.realtime-section {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 2rem;
}

.realtime-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.realtime-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.realtime-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.realtime-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.realtime-card {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius);
    color: var(--white);
}

.realtime-card h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.realtime-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Panels */
.notifications-panel,
.quick-actions-panel {
    position: fixed;
    top: 70px;
    left: -400px;
    width: 400px;
    height: calc(100vh - 70px);
    background: var(--white);
    box-shadow: var(--shadow);
    z-index: 1002;
    transition: var(--transition);
    overflow-y: auto;
}

.notifications-panel.open,
.quick-actions-panel.open {
    left: 0;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: var(--bg-light);
}

.panel-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.notifications-list {
    padding: 1rem;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: var(--transition);
}

.notification-item:hover {
    background: var(--bg-light);
}

.notification-item.unread {
    background: #f8f9ff;
    border-right: 3px solid var(--primary-color);
}

.actions-grid {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    background: var(--bg-light);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.action-btn i {
    font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .charts-row {
        grid-template-columns: 1fr;
    }
    
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .top-nav {
        padding: 0 1rem;
    }
    
    .nav-center {
        display: none;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .realtime-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .notifications-panel,
    .quick-actions-panel {
        width: 100%;
        left: -100%;
    }
}
