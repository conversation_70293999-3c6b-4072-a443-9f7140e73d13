# نظام مطعم بازوكا 🍽️

نظام إدارة مطعم شامل ومتطور باللغة العربية، مصمم خصيصاً لمطعم بازوكا. يوفر تجربة مستخدم استثنائية للعملاء ولوحة إدارة متقدمة لأصحاب المطاعم.

## ✨ المميزات الرئيسية

### للعملاء:
- **واجهة مستخدم جذابة**: تصميم عصري ومتجاوب يعمل على جميع الأجهزة
- **نظام مصادقة متقدم**: تسجيل دخول آمن مع التحقق بخطوتين
- **إدارة الحسابات**: ملفات شخصية مع تاريخ الطلبات والمفضلة
- **قائمة طعام تفاعلية**: عرض الأطباق مع الصور والأسعار والأوصاف
- **نظام طلبات متقدم**: سلة تسوق ذكية مع إمكانية تعديل الكميات
- **نظام حجز الطاولات**: حجز سهل وسريع مع تأكيد فوري
- **تصفية الأطباق**: تصنيف الأطباق حسب الفئات (مقبلات، أطباق رئيسية، حلويات، مشروبات)
- **تجربة تسوق سلسة**: عملية طلب مبسطة مع خيارات دفع متعددة
- **أمان متقدم**: حماية من محاولات الاختراق وتشفير البيانات

### للإدارة:
- **لوحة تحكم متقدمة**: واجهة احترافية مع رسوم بيانية تفاعلية
- **إحصائيات مباشرة**: بيانات فورية ومؤشرات أداء في الوقت الفعلي
- **بحث ذكي**: بحث شامل في جميع أجزاء النظام مع اقتراحات
- **إشعارات متقدمة**: تنبيهات ذكية للأحداث المهمة
- **إدارة الطلبات**: متابعة الطلبات من الاستلام حتى التوصيل
- **إدارة القائمة**: إضافة وتعديل وحذف الأطباق بسهولة
- **إدارة الحجوزات**: متابعة وتأكيد حجوزات الطاولات
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء
- **تقارير مفصلة**: تقارير المبيعات والأطباق الأكثر طلباً
- **إعدادات مرنة**: تخصيص معلومات المطعم وساعات العمل
- **أمان متقدم**: مراقبة الأمان وسجلات النظام

## 🛠️ التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق مع متغيرات CSS
- **JavaScript (ES6+)**: الوظائف التفاعلية والديناميكية
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo للنصوص العربية
- **Unsplash**: صور عالية الجودة للأطباق
- **LocalStorage**: حفظ بيانات السلة والإعدادات

## 📁 هيكل المشروع

```
مطعم/
├── index.html              # الصفحة الرئيسية
├── login.html              # صفحة تسجيل الدخول الموحدة
├── admin-login.html        # صفحة تسجيل دخول الإدارة
├── dashboard.html          # لوحة التحكم المتقدمة
├── admin.html              # لوحة الإدارة التقليدية
├── test.html               # صفحة اختبار النظام
├── README.md               # ملف التوثيق
├── package.json            # معلومات المشروع
├── styles/
│   ├── main.css           # الأنماط الرئيسية
│   ├── admin.css          # أنماط لوحة الإدارة
│   ├── auth.css           # أنماط نظام المصادقة
│   └── dashboard.css      # أنماط لوحة التحكم المتقدمة
├── scripts/
│   ├── main.js            # الوظائف الرئيسية
│   ├── menu.js            # إدارة القائمة
│   ├── cart.js            # إدارة السلة
│   ├── admin.js           # وظائف لوحة الإدارة
│   ├── auth.js            # نظام المصادقة المتقدم
│   └── dashboard.js       # وظائف لوحة التحكم المتقدمة
└── images/
    └── placeholder.txt    # مجلد الصور
```

## 🚀 كيفية التشغيل

1. **تحميل المشروع**:
   ```bash
   git clone [repository-url]
   cd مطعم
   ```

2. **فتح الموقع**:
   - افتح ملف `index.html` في المتصفح للصفحة الرئيسية
   - افتح ملف `admin.html` للوصول إلى لوحة الإدارة

3. **أو استخدام خادم محلي**:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx serve .
   
   # باستخدام PHP
   php -S localhost:8000
   ```

## 🔐 نظام المصادقة المتقدم

### مميزات الأمان:
- **تسجيل دخول آمن**: نظام مصادقة متعدد المستويات
- **التحقق بخطوتين**: رموز تحقق عبر الرسائل النصية
- **حماية من الهجمات**: منع محاولات الدخول المتكررة
- **تشفير البيانات**: حماية معلومات المستخدمين
- **جلسات آمنة**: انتهاء صلاحية تلقائي للجلسات
- **مراقبة الأمان**: تسجيل الأنشطة المشبوهة

### حسابات تجريبية:
#### للعملاء:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: User@123

#### للإدارة:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Admin@123
- **رمز التحقق**: 123456 (اختياري)

### مستويات الأمان:
1. **مستوى أساسي**: بريد إلكتروني وكلمة مرور
2. **مستوى متقدم**: إضافة التحقق بخطوتين
3. **مستوى إداري**: مصادقة إضافية للمدراء

## 🚀 لوحة التحكم المتقدمة

### المميزات الرئيسية:
- **واجهة حديثة ومتجاوبة**: تصميم عصري مع تأثيرات بصرية متقدمة
- **إحصائيات مباشرة**: بيانات فورية ومؤشرات أداء في الوقت الفعلي
- **رسوم بيانية تفاعلية**: مخططات متقدمة باستخدام Chart.js و ApexCharts
- **بحث ذكي**: بحث شامل في جميع أجزاء النظام مع اقتراحات فورية
- **إشعارات متقدمة**: تنبيهات ذكية للأحداث المهمة
- **إجراءات سريعة**: وصول سريع للمهام الشائعة
- **تحديثات مباشرة**: بيانات محدثة كل 5 ثوانٍ
- **تخصيص كامل**: إمكانية تخصيص الواجهة حسب الحاجة

### التقنيات المستخدمة:
- **Chart.js**: رسوم بيانية تفاعلية ومتحركة
- **ApexCharts**: مخططات متقدمة ومرنة
- **CSS Grid & Flexbox**: تخطيط متجاوب ومرن
- **JavaScript ES6+**: وظائف متقدمة وحديثة
- **Local Storage**: حفظ إعدادات المستخدم
- **Real-time Updates**: تحديثات فورية للبيانات

### الأقسام الرئيسية:
1. **لوحة التحكم الرئيسية**: إحصائيات شاملة ومؤشرات أداء
2. **التحليلات المتقدمة**: تقارير مفصلة ورسوم بيانية
3. **إدارة العمليات**: الطلبات، القائمة، الحجوزات، المخزون
4. **إدارة الأشخاص**: العملاء، الموظفين، الموردين
5. **التقارير المالية**: المبيعات، المصروفات، الضرائب
6. **إعدادات النظام**: الأمان، السجلات، التخصيص

## 📱 التوافق

- **المتصفحات**: Chrome, Firefox, Safari, Edge (الإصدارات الحديثة)
- **الأجهزة**: أجهزة سطح المكتب، الأجهزة اللوحية، الهواتف الذكية
- **الدقة**: تصميم متجاوب يدعم جميع أحجام الشاشات

## 🎨 التخصيص

### تغيير الألوان:
```css
:root {
    --primary-color: #ff6b35;    /* اللون الأساسي */
    --secondary-color: #2c3e50;  /* اللون الثانوي */
    --accent-color: #f39c12;     /* لون التمييز */
}
```

### إضافة أطباق جديدة:
```javascript
// في ملف scripts/menu.js
const newItem = {
    id: 21,
    name: 'اسم الطبق',
    description: 'وصف الطبق',
    price: 25.00,
    category: 'main',
    image: 'رابط الصورة',
    popular: false
};
```

## 📊 الميزات المتقدمة

### نظام الإشعارات:
- إشعارات فورية للعمليات المختلفة
- رسائل تأكيد للطلبات والحجوزات
- تنبيهات للأخطاء والتحذيرات

### نظام السلة الذكي:
- حفظ تلقائي للسلة في LocalStorage
- حساب تلقائي للضرائب ورسوم التوصيل
- إمكانية تعديل الكميات بسهولة

### لوحة الإدارة المتقدمة:
- إحصائيات في الوقت الفعلي
- فلترة وبحث متقدم
- واجهة سهلة الاستخدام

## 🔧 التطوير المستقبلي

- [ ] تكامل مع قواعد البيانات
- [ ] نظام دفع إلكتروني
- [ ] تطبيق جوال
- [ ] نظام تتبع التوصيل
- [ ] تقييمات العملاء
- [ ] برنامج الولاء
- [ ] تعدد اللغات
- [ ] تحليلات متقدمة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 123 4567
- **العنوان**: شارع الملك فهد، الرياض، المملكة العربية السعودية

## 🙏 شكر وتقدير

- [Font Awesome](https://fontawesome.com/) للأيقونات الرائعة
- [Google Fonts](https://fonts.google.com/) لخط Cairo
- [Unsplash](https://unsplash.com/) للصور عالية الجودة
- جميع المطورين الذين ساهموا في هذا المشروع

---

**مطعم بازوكا** - تجربة طعام استثنائية 🌟
