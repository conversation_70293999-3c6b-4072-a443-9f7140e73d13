/* Advanced Authentication Styles for Bazooka Restaurant */

/* User <PERSON>u Styles */
.user-menu {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.user-btn:hover {
    background: var(--bg-light);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 280px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
    border: 1px solid #e9ecef;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-content {
    padding: 1rem;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    color: var(--text-dark);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.user-dropdown a:hover {
    background: var(--bg-light);
    color: var(--primary-color);
}

.user-dropdown hr {
    margin: 0.5rem 0;
    border: none;
    border-top: 1px solid #e9ecef;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-display-name {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1rem;
}

.user-email {
    font-size: 0.85rem;
    color: var(--text-light);
}

.logout-link {
    color: #e74c3c !important;
}

.logout-link:hover {
    background: #fdf2f2 !important;
    color: #c0392b !important;
}

.admin-menu-item {
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    margin: 0.5rem 0;
    padding: 0.5rem 0;
}

.admin-menu-item a {
    color: var(--primary-color) !important;
    font-weight: 600;
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    border-radius: var(--border-radius);
    margin: 0 0.5rem;
}

.admin-menu-item a:hover {
    background: linear-gradient(135deg, var(--primary-color), #e55a2b) !important;
    color: var(--white) !important;
    transform: translateX(-2px);
}

/* Auth Modal Styles */
.auth-modal {
    z-index: 1005;
}

.auth-modal-content {
    max-width: 450px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 15px;
    color: var(--text-light);
    z-index: 2;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white);
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.input-group input.valid {
    border-color: #27ae60;
}

.input-group input.invalid {
    border-color: #e74c3c;
}

.password-toggle {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 5px;
    z-index: 3;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--primary-color);
}

.validation-icon {
    position: absolute;
    left: 15px;
    width: 20px;
    height: 20px;
    z-index: 2;
}

.validation-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    opacity: 0;
    transition: var(--transition);
}

.input-group.valid .validation-icon::after {
    content: '✓';
    background: #27ae60;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    opacity: 1;
}

.input-group.invalid .validation-icon::after {
    content: '✗';
    background: #e74c3c;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    opacity: 1;
}

.field-validation {
    font-size: 0.8rem;
    margin-top: 0.25rem;
    min-height: 1.2rem;
}

.field-validation.valid {
    color: #27ae60;
}

.field-validation.invalid {
    color: #e74c3c;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.country-code {
    width: 120px;
    border-left: 1px solid #e9ecef;
    padding: 15px 10px;
    background: var(--bg-light);
    font-size: 0.9rem;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-dark);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.radio-group {
    display: flex;
    gap: 2rem;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-dark);
}

.radio-label input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    position: relative;
    transition: var(--transition);
}

.radio-label input[type="radio"]:checked + .radio-custom {
    border-color: var(--primary-color);
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background: var(--primary-color);
    border-radius: 50%;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    text-decoration: underline;
}

.auth-btn {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.auth-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: var(--white);
    padding: 0 1rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.social-login {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.btn-social {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 12px;
    border: 2px solid #e9ecef;
    background: var(--white);
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
}

.btn-social:hover {
    border-color: var(--primary-color);
    background: var(--bg-light);
}

.google-btn:hover {
    border-color: #db4437;
    background: #fdf2f2;
    color: #db4437;
}

.facebook-btn:hover {
    border-color: #4267B2;
    background: #f0f4ff;
    color: #4267B2;
}

.auth-switch {
    text-align: center;
    margin-top: 1rem;
}

.auth-switch p {
    color: var(--text-light);
    font-size: 0.9rem;
}

.auth-switch a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.auth-switch a:hover {
    text-decoration: underline;
}

.form-help {
    color: var(--text-light);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.terms-label {
    font-size: 0.85rem;
    line-height: 1.4;
}

.terms-label a {
    color: var(--primary-color);
    text-decoration: none;
}

.terms-label a:hover {
    text-decoration: underline;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: var(--transition);
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: #e74c3c;
}

.strength-fill.fair {
    width: 50%;
    background: #f39c12;
}

.strength-fill.good {
    width: 75%;
    background: #f1c40f;
}

.strength-fill.strong {
    width: 100%;
    background: #27ae60;
}

.strength-text {
    font-size: 0.8rem;
    margin-top: 0.25rem;
    color: var(--text-light);
}

.password-requirements {
    margin-top: 0.75rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.25rem;
}

.requirement {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-light);
}

.requirement i {
    width: 12px;
    font-size: 10px;
}

.requirement.valid {
    color: #27ae60;
}

.requirement.valid i {
    color: #27ae60;
}

.requirement.valid i::before {
    content: '\f00c';
}

/* Two Factor Authentication */
.two-factor-content {
    text-align: center;
}

.two-factor-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.two-factor-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.two-factor-content p {
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.5;
}

.verification-code {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 2rem 0;
}

.code-input {
    width: 50px;
    height: 60px;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.code-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.code-input.filled {
    border-color: var(--primary-color);
    background: var(--bg-light);
}

.resend-code {
    margin: 1.5rem 0;
}

.resend-code p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.resend-code a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.resend-code a:hover {
    text-decoration: underline;
}

.countdown {
    font-size: 0.8rem;
    color: var(--text-light);
}

.countdown span {
    font-weight: 600;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .user-dropdown {
        left: -200px;
        min-width: 250px;
    }
    
    .verification-code {
        gap: 0.25rem;
    }
    
    .code-input {
        width: 40px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .password-requirements {
        grid-template-columns: 1fr;
    }
    
    .radio-group {
        gap: 1rem;
    }
}

/* Animation for form validation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.input-group.error {
    animation: shake 0.5s ease-in-out;
}

/* Success animation */
@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.validation-icon.success::after {
    animation: checkmark 0.3s ease-in-out;
}
