/* Admin Panel Styles for Bazooka Restaurant */

.admin-body {
    background: #f8f9fa;
    font-family: 'Cairo', sans-serif;
    margin: 0;
    padding: 0;
}

/* Admin Header */
.admin-header {
    background: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
}

.admin-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 2rem;
    max-width: none;
}

.admin-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.admin-logo i {
    margin-left: 10px;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.logout-btn:hover {
    background: #e55a2b;
}

/* Admin Sidebar */
.admin-sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: var(--white);
    box-shadow: var(--shadow);
    overflow-y: auto;
    z-index: 999;
}

.admin-menu ul {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
}

.admin-menu li {
    margin-bottom: 0.5rem;
}

.admin-menu a {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.admin-menu a:hover,
.admin-menu li.active a {
    background: var(--bg-light);
    color: var(--primary-color);
}

.admin-menu a i {
    margin-left: 12px;
    width: 20px;
    text-align: center;
}

.admin-menu .badge {
    background: var(--primary-color);
    color: var(--white);
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.8rem;
    margin-right: auto;
    margin-left: 10px;
}

/* Main Content */
.admin-main {
    margin-right: 280px;
    margin-top: 70px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--bg-light);
}

.section-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.section-header p {
    color: var(--text-light);
    margin: 0.5rem 0 0 0;
}

.section-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.section-actions select,
.section-actions input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-family: inherit;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0 0 0.5rem 0;
    font-weight: 500;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-dark);
    display: block;
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: #27ae60;
}

.stat-change.negative {
    color: #e74c3c;
}

.stat-change.neutral {
    color: var(--text-light);
}

/* Dashboard Widgets */
.dashboard-widgets {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.widget {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--bg-light);
}

.widget-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.widget-header a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.widget-header a:hover {
    color: #e55a2b;
}

.widget-content {
    padding: 1.5rem;
}

/* Orders */
.orders-grid {
    display: grid;
    gap: 1rem;
}

.order-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    border-right: 4px solid var(--primary-color);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.order-number {
    font-weight: 700;
    color: var(--text-dark);
}

.order-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.order-status.preparing {
    background: #d1ecf1;
    color: #0c5460;
}

.order-status.ready {
    background: #d4edda;
    color: #155724;
}

.order-status.delivered {
    background: #e2e3e5;
    color: #383d41;
}

.order-items {
    margin: 1rem 0;
    color: var(--text-light);
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--bg-light);
}

.order-total {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.order-actions {
    display: flex;
    gap: 0.5rem;
}

.order-actions button {
    padding: 6px 12px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.btn-accept {
    background: #27ae60;
    color: var(--white);
}

.btn-reject {
    background: #e74c3c;
    color: var(--white);
}

.btn-complete {
    background: var(--primary-color);
    color: var(--white);
}

/* Menu Management */
.menu-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.menu-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.admin-menu-item {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.admin-menu-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.admin-menu-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.admin-menu-item-content {
    padding: 1.5rem;
}

.admin-menu-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.admin-menu-item p {
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.admin-menu-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--bg-light);
}

.admin-menu-item .price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.admin-menu-item-actions {
    display: flex;
    gap: 0.5rem;
}

.admin-menu-item-actions button {
    padding: 6px 10px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.btn-edit {
    background: var(--accent-color);
    color: var(--white);
}

.btn-delete {
    background: #e74c3c;
    color: var(--white);
}

.btn-toggle {
    background: var(--secondary-color);
    color: var(--white);
}

/* Reservations */
.reservations-grid {
    display: grid;
    gap: 1rem;
}

.reservation-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    border-right: 4px solid var(--accent-color);
}

.reservation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.reservation-name {
    font-weight: 700;
    color: var(--text-dark);
}

.reservation-time {
    color: var(--text-light);
    font-size: 0.9rem;
}

.reservation-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.reservation-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
}

.reservation-detail i {
    color: var(--primary-color);
}

.reservation-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--bg-light);
}

/* Customers Table */
.customers-table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.customers-table {
    width: 100%;
    border-collapse: collapse;
}

.customers-table th,
.customers-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--bg-light);
}

.customers-table th {
    background: var(--bg-light);
    font-weight: 600;
    color: var(--text-dark);
}

.customers-table tr:hover {
    background: #f8f9fa;
}

/* Reports */
.reports-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.report-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
}

.report-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    color: var(--text-light);
}

/* Settings */
.settings-container {
    display: grid;
    gap: 2rem;
}

.settings-group {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
}

.settings-group h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
    border-bottom: 2px solid var(--bg-light);
    padding-bottom: 0.5rem;
}

.settings-form {
    display: grid;
    gap: 1rem;
}

.settings-form input,
.settings-form select,
.settings-form textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-family: inherit;
    transition: var(--transition);
}

.settings-form input:focus,
.settings-form select:focus,
.settings-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.time-settings {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.time-settings label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

/* Item Form Modal */
.item-form {
    display: grid;
    gap: 1rem;
}

.item-form input,
.item-form select,
.item-form textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-family: inherit;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-label input {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-sidebar {
        width: 250px;
    }
    
    .admin-main {
        margin-right: 250px;
    }
    
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-right: 0;
        padding: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .menu-items-grid {
        grid-template-columns: 1fr;
    }
    
    .customers-table-container {
        overflow-x: auto;
    }
}

/* Additional Utility Classes */
.text-center {
    text-align: center;
}

.text-success {
    color: #27ae60;
}

.text-danger {
    color: #e74c3c;
}

.text-warning {
    color: #f39c12;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }

.d-flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.align-center {
    align-items: center;
}

.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }

.w-100 { width: 100%; }

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.1rem;
}
