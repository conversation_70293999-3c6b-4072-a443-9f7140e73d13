// Cart management for Bazooka Restaurant

// Cart state
let cartState = {
    items: [],
    total: 0,
    itemCount: 0
};

// Initialize cart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCart();
    setupCartEventListeners();
});

// Initialize cart
function initializeCart() {
    loadCartFromStorage();
    updateCartDisplay();
    renderCartItems();
}

// Setup cart event listeners
function setupCartEventListeners() {
    // Cart overlay click to close
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('cart-overlay')) {
            toggleCart();
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Escape key to close cart
        if (e.key === 'Escape' && document.getElementById('cart-sidebar').classList.contains('open')) {
            toggleCart();
        }
    });
}

// Load cart from localStorage
function loadCartFromStorage() {
    const savedCart = localStorage.getItem('bazookaCart');
    if (savedCart) {
        try {
            cartState.items = JSON.parse(savedCart);
            calculateCartTotals();
        } catch (error) {
            console.error('Error loading cart from storage:', error);
            cartState.items = [];
        }
    }
}

// Save cart to localStorage
function saveCartToStorage() {
    try {
        localStorage.setItem('bazookaCart', JSON.stringify(cartState.items));
    } catch (error) {
        console.error('Error saving cart to storage:', error);
        showNotification('خطأ في حفظ السلة', 'error');
    }
}

// Calculate cart totals
function calculateCartTotals() {
    cartState.itemCount = cartState.items.reduce((sum, item) => sum + item.quantity, 0);
    cartState.total = cartState.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
}

// Add item to cart
function addItemToCart(item) {
    const existingItemIndex = cartState.items.findIndex(cartItem => cartItem.id === item.id);
    
    if (existingItemIndex > -1) {
        cartState.items[existingItemIndex].quantity += 1;
        showNotification(`تم زيادة كمية ${item.name}`, 'success');
    } else {
        cartState.items.push({
            ...item,
            quantity: 1,
            addedAt: new Date().toISOString()
        });
        showNotification(`تم إضافة ${item.name} إلى السلة`, 'success');
    }
    
    calculateCartTotals();
    saveCartToStorage();
    updateCartDisplay();
    renderCartItems();
    
    // Add animation to cart icon
    animateCartIcon();
}

// Remove item from cart
function removeItemFromCart(itemId) {
    const itemIndex = cartState.items.findIndex(item => item.id === itemId);
    
    if (itemIndex > -1) {
        const itemName = cartState.items[itemIndex].name;
        cartState.items.splice(itemIndex, 1);
        
        calculateCartTotals();
        saveCartToStorage();
        updateCartDisplay();
        renderCartItems();
        
        showNotification(`تم حذف ${itemName} من السلة`, 'info');
    }
}

// Update item quantity
function updateItemQuantity(itemId, newQuantity) {
    const itemIndex = cartState.items.findIndex(item => item.id === itemId);
    
    if (itemIndex > -1) {
        if (newQuantity <= 0) {
            removeItemFromCart(itemId);
        } else {
            cartState.items[itemIndex].quantity = newQuantity;
            
            calculateCartTotals();
            saveCartToStorage();
            updateCartDisplay();
            renderCartItems();
            
            showNotification('تم تحديث الكمية', 'success');
        }
    }
}

// Clear entire cart
function clearCart() {
    if (cartState.items.length === 0) {
        showNotification('السلة فارغة بالفعل', 'info');
        return;
    }
    
    if (confirm('هل أنت متأكد من حذف جميع العناصر من السلة؟')) {
        cartState.items = [];
        calculateCartTotals();
        saveCartToStorage();
        updateCartDisplay();
        renderCartItems();
        
        showNotification('تم حذف جميع العناصر من السلة', 'info');
    }
}

// Update cart display (counter and total)
function updateCartDisplay() {
    const cartCount = document.querySelector('.cart-count');
    const cartTotal = document.getElementById('cart-total');
    
    if (cartCount) {
        cartCount.textContent = cartState.itemCount;
        
        // Add animation when count changes
        cartCount.style.transform = 'scale(1.2)';
        setTimeout(() => {
            cartCount.style.transform = 'scale(1)';
        }, 200);
    }
    
    if (cartTotal) {
        cartTotal.textContent = cartState.total.toFixed(2);
    }
}

// Render cart items in sidebar
function renderCartItems() {
    const cartItemsContainer = document.getElementById('cart-items');
    if (!cartItemsContainer) return;
    
    if (cartState.items.length === 0) {
        cartItemsContainer.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <h3>السلة فارغة</h3>
                <p>أضف بعض الأطباق الشهية من القائمة!</p>
                <button class="btn btn-primary" onclick="scrollToSection('menu'); toggleCart();">
                    تصفح القائمة
                </button>
            </div>
        `;
        return;
    }
    
    cartItemsContainer.innerHTML = cartState.items.map(item => `
        <div class="cart-item" data-item-id="${item.id}">
            <div class="cart-item-image">
                <img src="${item.image}" alt="${item.name}" loading="lazy">
            </div>
            <div class="cart-item-details">
                <h4 class="cart-item-name">${item.name}</h4>
                <p class="cart-item-price">${item.price.toFixed(2)} ريال</p>
                <div class="cart-item-controls">
                    <button class="quantity-btn decrease" onclick="updateItemQuantity(${item.id}, ${item.quantity - 1})" 
                            ${item.quantity <= 1 ? 'disabled' : ''}>
                        <i class="fas fa-minus"></i>
                    </button>
                    <span class="quantity-display">${item.quantity}</span>
                    <button class="quantity-btn increase" onclick="updateItemQuantity(${item.id}, ${item.quantity + 1})">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="remove-item" onclick="removeItemFromCart(${item.id})" title="حذف العنصر">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="cart-item-total">
                    المجموع: ${(item.price * item.quantity).toFixed(2)} ريال
                </div>
            </div>
        </div>
    `).join('');
    
    // Add clear cart button if there are items
    if (cartState.items.length > 0) {
        cartItemsContainer.innerHTML += `
            <div class="cart-actions">
                <button class="btn btn-secondary clear-cart" onclick="clearCart()">
                    <i class="fas fa-trash"></i> إفراغ السلة
                </button>
            </div>
        `;
    }
}

// Animate cart icon when item is added
function animateCartIcon() {
    const cartBtn = document.querySelector('.cart-btn');
    if (cartBtn) {
        cartBtn.style.transform = 'scale(1.1)';
        cartBtn.style.background = '#27ae60';
        
        setTimeout(() => {
            cartBtn.style.transform = 'scale(1)';
            cartBtn.style.background = '';
        }, 300);
    }
}

// Get cart summary for checkout
function getCartSummary() {
    return {
        items: cartState.items,
        itemCount: cartState.itemCount,
        subtotal: cartState.total,
        tax: cartState.total * 0.15, // 15% VAT
        delivery: cartState.total > 100 ? 0 : 15, // Free delivery over 100 SAR
        total: cartState.total + (cartState.total * 0.15) + (cartState.total > 100 ? 0 : 15)
    };
}

// Process checkout
function processCheckout() {
    if (cartState.items.length === 0) {
        showNotification('السلة فارغة! أضف بعض الأطباق أولاً.', 'error');
        return;
    }
    
    const summary = getCartSummary();
    showCheckoutModal(summary);
}

// Show checkout modal
function showCheckoutModal(summary) {
    const modal = document.createElement('div');
    modal.className = 'modal checkout-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-receipt"></i> تأكيد الطلب</h3>
                <button class="modal-close" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body">
                <div class="order-summary">
                    <h4>ملخص الطلب:</h4>
                    <div class="order-items">
                        ${summary.items.map(item => `
                            <div class="order-item">
                                <span class="item-name">${item.name}</span>
                                <span class="item-quantity">x${item.quantity}</span>
                                <span class="item-total">${(item.price * item.quantity).toFixed(2)} ريال</span>
                            </div>
                        `).join('')}
                    </div>
                    <div class="order-totals">
                        <div class="total-line">
                            <span>المجموع الفرعي:</span>
                            <span>${summary.subtotal.toFixed(2)} ريال</span>
                        </div>
                        <div class="total-line">
                            <span>ضريبة القيمة المضافة (15%):</span>
                            <span>${summary.tax.toFixed(2)} ريال</span>
                        </div>
                        <div class="total-line">
                            <span>رسوم التوصيل:</span>
                            <span>${summary.delivery === 0 ? 'مجاني' : summary.delivery.toFixed(2) + ' ريال'}</span>
                        </div>
                        <div class="total-line final-total">
                            <span>المجموع الكلي:</span>
                            <span>${summary.total.toFixed(2)} ريال</span>
                        </div>
                    </div>
                </div>
                
                <form id="checkout-form" class="checkout-form">
                    <h4>معلومات التوصيل:</h4>
                    <div class="form-row">
                        <input type="text" name="fullName" placeholder="الاسم الكامل" required>
                        <input type="tel" name="phone" placeholder="رقم الهاتف" required>
                    </div>
                    <input type="email" name="email" placeholder="البريد الإلكتروني (اختياري)">
                    <textarea name="address" placeholder="العنوان التفصيلي" required rows="3"></textarea>
                    <textarea name="notes" placeholder="ملاحظات إضافية (اختياري)" rows="2"></textarea>
                    
                    <div class="payment-method">
                        <h4>طريقة الدفع:</h4>
                        <label class="payment-option">
                            <input type="radio" name="payment" value="cash" checked>
                            <span>الدفع عند الاستلام</span>
                        </label>
                        <label class="payment-option">
                            <input type="radio" name="payment" value="card">
                            <span>بطاقة ائتمانية</span>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary checkout-submit">
                        <i class="fas fa-check"></i> تأكيد الطلب
                    </button>
                </form>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Show modal
    setTimeout(() => {
        modal.classList.add('show');
    }, 100);
    
    // Handle form submission
    const checkoutForm = modal.querySelector('#checkout-form');
    checkoutForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleCheckoutSubmission(e.target, summary);
    });
}

// Handle checkout form submission
function handleCheckoutSubmission(form, summary) {
    const formData = new FormData(form);
    const orderData = Object.fromEntries(formData);
    
    // Add loading state
    const submitBtn = form.querySelector('.checkout-submit');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<div class="loading"></div> جاري معالجة الطلب...';
    submitBtn.disabled = true;
    
    // Simulate order processing
    setTimeout(() => {
        // Generate order number
        const orderNumber = 'BZ' + Date.now().toString().slice(-6);
        
        // Clear cart
        cartState.items = [];
        calculateCartTotals();
        saveCartToStorage();
        updateCartDisplay();
        renderCartItems();
        
        // Close modal and cart
        closeModal(submitBtn);
        if (document.getElementById('cart-sidebar').classList.contains('open')) {
            toggleCart();
        }
        
        // Show success message
        showOrderConfirmation(orderNumber, orderData, summary);
        
    }, 2000);
}

// Show order confirmation
function showOrderConfirmation(orderNumber, orderData, summary) {
    const modal = document.createElement('div');
    modal.className = 'modal confirmation-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-check-circle" style="color: #27ae60;"></i> تم تأكيد طلبك!</h3>
                <button class="modal-close" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body">
                <div class="confirmation-content">
                    <div class="order-number">
                        <h4>رقم الطلب: <span>${orderNumber}</span></h4>
                    </div>
                    <p>شكراً لك ${orderData.fullName}! تم استلام طلبك بنجاح.</p>
                    <p>سنتواصل معك على رقم ${orderData.phone} لتأكيد التوصيل.</p>
                    <p>وقت التوصيل المتوقع: 30-45 دقيقة</p>
                    <div class="order-total">
                        المبلغ الإجمالي: ${summary.total.toFixed(2)} ريال
                    </div>
                    <button class="btn btn-primary" onclick="closeModal(this)">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Show modal
    setTimeout(() => {
        modal.classList.add('show');
    }, 100);
}

// Add CSS for cart components
const cartStyles = document.createElement('style');
cartStyles.textContent = `
    .empty-cart {
        text-align: center;
        padding: 3rem 1rem;
        color: var(--text-light);
    }
    
    .empty-cart i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.3;
    }
    
    .empty-cart h3 {
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }
    
    .cart-item {
        display: flex;
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
        gap: 1rem;
    }
    
    .cart-item-image img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 8px;
    }
    
    .cart-item-details {
        flex: 1;
    }
    
    .cart-item-name {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: var(--text-dark);
    }
    
    .cart-item-price {
        font-size: 0.9rem;
        color: var(--text-light);
        margin-bottom: 0.5rem;
    }
    
    .cart-item-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .quantity-btn {
        width: 28px;
        height: 28px;
        border: none;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
    }
    
    .quantity-btn:hover:not(:disabled) {
        background: #e55a2b;
        transform: scale(1.1);
    }
    
    .quantity-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
    }
    
    .quantity-display {
        min-width: 30px;
        text-align: center;
        font-weight: 600;
    }
    
    .remove-item {
        background: #e74c3c;
        color: white;
        border: none;
        padding: 5px 8px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: auto;
        transition: var(--transition);
    }
    
    .remove-item:hover {
        background: #c0392b;
    }
    
    .cart-item-total {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--primary-color);
    }
    
    .cart-actions {
        padding: 1rem;
        border-top: 1px solid #e9ecef;
    }
    
    .clear-cart {
        width: 100%;
        background: #e74c3c;
        color: white;
    }
    
    .clear-cart:hover {
        background: #c0392b;
    }
    
    .order-summary {
        margin-bottom: 2rem;
    }
    
    .order-items {
        margin: 1rem 0;
    }
    
    .order-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .order-totals {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 2px solid #e9ecef;
    }
    
    .total-line {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }
    
    .final-total {
        font-weight: 700;
        font-size: 1.1rem;
        color: var(--primary-color);
        border-top: 1px solid #e9ecef;
        padding-top: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .checkout-form {
        margin-top: 2rem;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .payment-method {
        margin: 1.5rem 0;
    }
    
    .payment-option {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        cursor: pointer;
    }
    
    .payment-option input {
        margin-left: 0.5rem;
    }
    
    .confirmation-content {
        text-align: center;
    }
    
    .order-number {
        background: var(--bg-light);
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }
    
    .order-number span {
        color: var(--primary-color);
        font-weight: 700;
    }
    
    .order-total {
        background: var(--primary-color);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 1.5rem 0;
        font-size: 1.2rem;
        font-weight: 600;
    }
`;
document.head.appendChild(cartStyles);
