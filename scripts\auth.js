// Advanced Authentication System for Bazooka Restaurant

// Authentication state
let authState = {
    isLoggedIn: false,
    currentUser: null,
    loginAttempts: 0,
    maxLoginAttempts: 5,
    lockoutTime: 15 * 60 * 1000, // 15 minutes
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    twoFactorEnabled: false,
    rememberMe: false
};

// Sample users database (in real app, this would be server-side)
const usersDatabase = [
    {
        id: 1,
        email: '<EMAIL>',
        phone: '+966501234567',
        password: 'Admin@123', // In real app, this would be hashed
        firstName: 'أحمد',
        lastName: 'المدير',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        role: 'admin',
        isVerified: true,
        twoFactorEnabled: true,
        createdAt: '2023-01-01',
        lastLogin: null,
        preferences: {
            language: 'ar',
            notifications: true,
            newsletter: true
        }
    },
    {
        id: 2,
        email: '<EMAIL>',
        phone: '+966507654321',
        password: 'User@123',
        firstName: 'سارة',
        lastName: 'أحمد',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        role: 'customer',
        isVerified: true,
        twoFactorEnabled: false,
        createdAt: '2023-06-15',
        lastLogin: null,
        preferences: {
            language: 'ar',
            notifications: true,
            newsletter: false
        }
    }
];

// Initialize authentication system
document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
    setupAuthEventListeners();
    checkExistingSession();
});

// Initialize authentication
function initializeAuth() {
    console.log('🔐 Advanced Authentication System Initialized');
    
    // Check for account lockout
    checkAccountLockout();
    
    // Setup session timeout
    setupSessionTimeout();
    
    // Setup password strength checker
    setupPasswordStrengthChecker();
    
    // Setup two-factor authentication
    setupTwoFactorAuth();
}

// Setup event listeners
function setupAuthEventListeners() {
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Register form
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // Forgot password form
    const forgotPasswordForm = document.getElementById('forgot-password-form');
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleForgotPassword);
    }
    
    // Two factor form
    const twoFactorForm = document.getElementById('two-factor-form');
    if (twoFactorForm) {
        twoFactorForm.addEventListener('submit', handleTwoFactorVerification);
    }
    
    // Real-time validation
    setupRealTimeValidation();
    
    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('auth-modal')) {
            closeAuthModal(e.target.id);
        }
    });
}

// Check existing session
function checkExistingSession() {
    const savedSession = localStorage.getItem('bazookaSession');
    const rememberMe = localStorage.getItem('bazookaRememberMe');
    
    if (savedSession) {
        try {
            const session = JSON.parse(savedSession);
            const now = new Date().getTime();
            
            // Check if session is still valid
            if (now - session.timestamp < authState.sessionTimeout || rememberMe) {
                const user = usersDatabase.find(u => u.id === session.userId);
                if (user) {
                    loginUser(user, false);
                }
            } else {
                // Session expired
                clearSession();
            }
        } catch (error) {
            console.error('Error parsing session:', error);
            clearSession();
        }
    }
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const email = formData.get('email').trim();
    const password = formData.get('password');
    const rememberMe = formData.get('remember');
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    showButtonLoading(submitBtn);
    
    try {
        // Check account lockout
        if (isAccountLocked()) {
            throw new Error('الحساب مقفل مؤقتاً بسبب محاولات دخول خاطئة متعددة');
        }
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Find user
        const user = usersDatabase.find(u => 
            u.email === email || u.phone === email
        );
        
        if (!user || user.password !== password) {
            authState.loginAttempts++;
            localStorage.setItem('loginAttempts', authState.loginAttempts.toString());
            localStorage.setItem('lastAttempt', new Date().getTime().toString());
            
            throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة');
        }
        
        // Reset login attempts on successful login
        authState.loginAttempts = 0;
        localStorage.removeItem('loginAttempts');
        localStorage.removeItem('lastAttempt');
        
        // Check if two-factor is enabled
        if (user.twoFactorEnabled) {
            // Send verification code (simulated)
            sendTwoFactorCode(user);
            closeAuthModal('login-modal');
            showTwoFactorModal(user);
        } else {
            // Login successful
            loginUser(user, rememberMe);
            closeAuthModal('login-modal');
            showNotification(`مرحباً بك ${user.firstName}!`, 'success');
        }
        
    } catch (error) {
        showNotification(error.message, 'error');
        
        // Shake form on error
        e.target.classList.add('error');
        setTimeout(() => {
            e.target.classList.remove('error');
        }, 500);
    } finally {
        hideButtonLoading(submitBtn);
    }
}

// Handle registration
async function handleRegister(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const userData = {
        firstName: formData.get('firstname').trim(),
        lastName: formData.get('lastname').trim(),
        email: formData.get('email').trim(),
        phone: formData.get('phone').trim(),
        password: formData.get('password'),
        confirmPassword: formData.get('confirmPassword'),
        birthdate: formData.get('birthdate'),
        gender: formData.get('gender'),
        terms: formData.get('terms'),
        newsletter: formData.get('newsletter')
    };
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    showButtonLoading(submitBtn);
    
    try {
        // Validate form data
        validateRegistrationData(userData);
        
        // Check if user already exists
        const existingUser = usersDatabase.find(u => 
            u.email === userData.email || u.phone === userData.phone
        );
        
        if (existingUser) {
            throw new Error('المستخدم موجود بالفعل بهذا البريد الإلكتروني أو رقم الهاتف');
        }
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Create new user
        const newUser = {
            id: usersDatabase.length + 1,
            email: userData.email,
            phone: userData.phone,
            password: userData.password, // In real app, hash this
            firstName: userData.firstName,
            lastName: userData.lastName,
            avatar: `https://ui-avatars.com/api/?name=${userData.firstName}+${userData.lastName}&background=ff6b35&color=fff&size=150`,
            role: 'customer',
            isVerified: false,
            twoFactorEnabled: false,
            createdAt: new Date().toISOString().split('T')[0],
            lastLogin: null,
            preferences: {
                language: 'ar',
                notifications: true,
                newsletter: userData.newsletter === 'on'
            }
        };
        
        // Add to database (in real app, this would be server-side)
        usersDatabase.push(newUser);
        
        // Send verification email (simulated)
        sendVerificationEmail(newUser);
        
        closeAuthModal('register-modal');
        showNotification('تم إنشاء الحساب بنجاح! تحقق من بريدك الإلكتروني للتفعيل.', 'success');
        
        // Auto login after registration
        setTimeout(() => {
            loginUser(newUser, false);
        }, 1000);
        
    } catch (error) {
        showNotification(error.message, 'error');
    } finally {
        hideButtonLoading(submitBtn);
    }
}

// Handle forgot password
async function handleForgotPassword(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const email = formData.get('email').trim();
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    showButtonLoading(submitBtn);
    
    try {
        // Find user
        const user = usersDatabase.find(u => u.email === email);
        
        if (!user) {
            throw new Error('لم يتم العثور على حساب بهذا البريد الإلكتروني');
        }
        
        // Simulate sending reset email
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        closeAuthModal('forgot-password-modal');
        showNotification('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني', 'success');
        
    } catch (error) {
        showNotification(error.message, 'error');
    } finally {
        hideButtonLoading(submitBtn);
    }
}

// Handle two-factor verification
async function handleTwoFactorVerification(e) {
    e.preventDefault();
    
    const codeInputs = document.querySelectorAll('.code-input');
    const code = Array.from(codeInputs).map(input => input.value).join('');
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    showButtonLoading(submitBtn);
    
    try {
        if (code.length !== 6) {
            throw new Error('يرجى إدخال الرمز كاملاً');
        }
        
        // Simulate verification
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // For demo, accept any 6-digit code
        if (!/^\d{6}$/.test(code)) {
            throw new Error('رمز التحقق غير صحيح');
        }
        
        // Get pending user from session storage
        const pendingUser = JSON.parse(sessionStorage.getItem('pendingTwoFactorUser'));
        
        if (pendingUser) {
            loginUser(pendingUser, false);
            sessionStorage.removeItem('pendingTwoFactorUser');
            closeAuthModal('two-factor-modal');
            showNotification(`مرحباً بك ${pendingUser.firstName}!`, 'success');
        }
        
    } catch (error) {
        showNotification(error.message, 'error');
        
        // Clear code inputs on error
        codeInputs.forEach(input => {
            input.value = '';
            input.classList.remove('filled');
        });
        codeInputs[0].focus();
        
    } finally {
        hideButtonLoading(submitBtn);
    }
}

// Login user
function loginUser(user, rememberMe = false) {
    authState.isLoggedIn = true;
    authState.currentUser = user;
    authState.rememberMe = rememberMe;
    
    // Update user's last login
    user.lastLogin = new Date().toISOString();
    
    // Save session
    const session = {
        userId: user.id,
        timestamp: new Date().getTime()
    };
    
    localStorage.setItem('bazookaSession', JSON.stringify(session));
    
    if (rememberMe) {
        localStorage.setItem('bazookaRememberMe', 'true');
    }
    
    // Update UI
    updateUserInterface();
    
    // Setup session timeout
    setupSessionTimeout();
}

// Logout user
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        authState.isLoggedIn = false;
        authState.currentUser = null;
        
        clearSession();
        updateUserInterface();
        
        showNotification('تم تسجيل الخروج بنجاح', 'info');
    }
}

// Clear session
function clearSession() {
    localStorage.removeItem('bazookaSession');
    localStorage.removeItem('bazookaRememberMe');
    sessionStorage.clear();
}

// Update user interface
function updateUserInterface() {
    const userNameElement = document.getElementById('user-name');
    const userDisplayNameElement = document.getElementById('user-display-name');
    const userEmailElement = document.getElementById('user-email');
    const userAvatarElement = document.getElementById('user-avatar');
    const guestMenu = document.getElementById('guest-menu');
    const userMenuLogged = document.getElementById('user-menu-logged');
    
    if (authState.isLoggedIn && authState.currentUser) {
        const user = authState.currentUser;
        
        // Update user name in navbar
        if (userNameElement) {
            userNameElement.textContent = user.firstName;
        }
        
        // Update user info in dropdown
        if (userDisplayNameElement) {
            userDisplayNameElement.textContent = `${user.firstName} ${user.lastName}`;
        }
        
        if (userEmailElement) {
            userEmailElement.textContent = user.email;
        }
        
        if (userAvatarElement) {
            userAvatarElement.src = user.avatar;
        }
        
        // Show/hide menus
        if (guestMenu) guestMenu.style.display = 'none';
        if (userMenuLogged) userMenuLogged.style.display = 'block';
        
    } else {
        // Reset to guest state
        if (userNameElement) {
            userNameElement.textContent = 'تسجيل دخول';
        }
        
        if (guestMenu) guestMenu.style.display = 'block';
        if (userMenuLogged) userMenuLogged.style.display = 'none';
    }
}

// Validate registration data
function validateRegistrationData(data) {
    const errors = [];
    
    // Name validation
    if (!data.firstName || data.firstName.length < 2) {
        errors.push('الاسم الأول يجب أن يكون حرفين على الأقل');
    }
    
    if (!data.lastName || data.lastName.length < 2) {
        errors.push('الاسم الأخير يجب أن يكون حرفين على الأقل');
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
        errors.push('البريد الإلكتروني غير صحيح');
    }
    
    // Phone validation
    const phoneRegex = /^[0-9]{9}$/;
    if (!phoneRegex.test(data.phone)) {
        errors.push('رقم الهاتف يجب أن يكون 9 أرقام');
    }
    
    // Password validation
    if (!isPasswordStrong(data.password)) {
        errors.push('كلمة المرور لا تلبي المتطلبات الأمنية');
    }
    
    // Confirm password
    if (data.password !== data.confirmPassword) {
        errors.push('كلمة المرور وتأكيدها غير متطابقتين');
    }
    
    // Terms acceptance
    if (!data.terms) {
        errors.push('يجب الموافقة على الشروط والأحكام');
    }
    
    if (errors.length > 0) {
        throw new Error(errors.join('\n'));
    }
}

// Check password strength
function isPasswordStrong(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
}

// Setup password strength checker
function setupPasswordStrengthChecker() {
    const passwordInput = document.getElementById('register-password');
    if (!passwordInput) return;
    
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        updatePasswordStrength(password);
        updatePasswordRequirements(password);
    });
}

// Update password strength indicator
function updatePasswordStrength(password) {
    const strengthFill = document.querySelector('.strength-fill');
    const strengthText = document.querySelector('.strength-text');
    
    if (!strengthFill || !strengthText) return;
    
    let strength = 0;
    let strengthLabel = '';
    
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
    
    // Remove all strength classes
    strengthFill.className = 'strength-fill';
    
    switch (strength) {
        case 0:
        case 1:
            strengthFill.classList.add('weak');
            strengthLabel = 'ضعيفة';
            break;
        case 2:
            strengthFill.classList.add('fair');
            strengthLabel = 'متوسطة';
            break;
        case 3:
        case 4:
            strengthFill.classList.add('good');
            strengthLabel = 'جيدة';
            break;
        case 5:
            strengthFill.classList.add('strong');
            strengthLabel = 'قوية';
            break;
    }
    
    strengthText.textContent = `قوة كلمة المرور: ${strengthLabel}`;
}

// Update password requirements
function updatePasswordRequirements(password) {
    const requirements = [
        { id: 'req-length', test: password.length >= 8 },
        { id: 'req-uppercase', test: /[A-Z]/.test(password) },
        { id: 'req-lowercase', test: /[a-z]/.test(password) },
        { id: 'req-number', test: /\d/.test(password) },
        { id: 'req-special', test: /[!@#$%^&*(),.?":{}|<>]/.test(password) }
    ];
    
    requirements.forEach(req => {
        const element = document.getElementById(req.id);
        if (element) {
            if (req.test) {
                element.classList.add('valid');
            } else {
                element.classList.remove('valid');
            }
        }
    });
}

// Setup real-time validation
function setupRealTimeValidation() {
    // Email validation
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateEmail(this);
        });
    });
    
    // Phone validation
    const phoneInput = document.getElementById('register-phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            validatePhone(this);
        });
    }
    
    // Confirm password validation
    const confirmPasswordInput = document.getElementById('register-confirm-password');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            validateConfirmPassword(this);
        });
    }
}

// Validate email
function validateEmail(input) {
    const email = input.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const inputGroup = input.closest('.input-group');
    const validation = input.parentNode.nextElementSibling;
    
    if (email && !emailRegex.test(email)) {
        inputGroup.classList.add('invalid');
        inputGroup.classList.remove('valid');
        if (validation) {
            validation.textContent = 'البريد الإلكتروني غير صحيح';
            validation.className = 'field-validation invalid';
        }
    } else if (email) {
        inputGroup.classList.add('valid');
        inputGroup.classList.remove('invalid');
        if (validation) {
            validation.textContent = 'البريد الإلكتروني صحيح';
            validation.className = 'field-validation valid';
        }
    } else {
        inputGroup.classList.remove('valid', 'invalid');
        if (validation) {
            validation.textContent = '';
            validation.className = 'field-validation';
        }
    }
}

// Validate phone
function validatePhone(input) {
    const phone = input.value.trim();
    const phoneRegex = /^[0-9]{9}$/;
    const inputGroup = input.closest('.input-group');
    const validation = input.parentNode.nextElementSibling;
    
    if (phone && !phoneRegex.test(phone)) {
        inputGroup.classList.add('invalid');
        inputGroup.classList.remove('valid');
        if (validation) {
            validation.textContent = 'رقم الهاتف يجب أن يكون 9 أرقام';
            validation.className = 'field-validation invalid';
        }
    } else if (phone) {
        inputGroup.classList.add('valid');
        inputGroup.classList.remove('invalid');
        if (validation) {
            validation.textContent = 'رقم الهاتف صحيح';
            validation.className = 'field-validation valid';
        }
    } else {
        inputGroup.classList.remove('valid', 'invalid');
        if (validation) {
            validation.textContent = '';
            validation.className = 'field-validation';
        }
    }
}

// Validate confirm password
function validateConfirmPassword(input) {
    const confirmPassword = input.value;
    const password = document.getElementById('register-password').value;
    const inputGroup = input.closest('.input-group');
    const validation = input.parentNode.nextElementSibling;
    
    if (confirmPassword && confirmPassword !== password) {
        inputGroup.classList.add('invalid');
        inputGroup.classList.remove('valid');
        if (validation) {
            validation.textContent = 'كلمة المرور غير متطابقة';
            validation.className = 'field-validation invalid';
        }
    } else if (confirmPassword) {
        inputGroup.classList.add('valid');
        inputGroup.classList.remove('invalid');
        if (validation) {
            validation.textContent = 'كلمة المرور متطابقة';
            validation.className = 'field-validation valid';
        }
    } else {
        inputGroup.classList.remove('valid', 'invalid');
        if (validation) {
            validation.textContent = '';
            validation.className = 'field-validation';
        }
    }
}

// Setup two-factor authentication
function setupTwoFactorAuth() {
    const codeInputs = document.querySelectorAll('.code-input');

    codeInputs.forEach((input, index) => {
        input.addEventListener('input', function() {
            if (this.value.length === 1) {
                this.classList.add('filled');

                // Move to next input
                if (index < codeInputs.length - 1) {
                    codeInputs[index + 1].focus();
                }
            } else {
                this.classList.remove('filled');
            }
        });

        input.addEventListener('keydown', function(e) {
            // Handle backspace
            if (e.key === 'Backspace' && this.value === '' && index > 0) {
                codeInputs[index - 1].focus();
            }
        });

        // Only allow numbers
        input.addEventListener('keypress', function(e) {
            if (!/\d/.test(e.key) && e.key !== 'Backspace') {
                e.preventDefault();
            }
        });
    });
}

// Send two-factor code
function sendTwoFactorCode(user) {
    // Simulate sending SMS
    console.log(`Sending 2FA code to ${user.phone}`);

    // Store user temporarily for verification
    sessionStorage.setItem('pendingTwoFactorUser', JSON.stringify(user));

    // Start countdown
    startResendCountdown();
}

// Start resend countdown
function startResendCountdown() {
    const countdownElement = document.querySelector('#resend-countdown span');
    const resendLink = document.querySelector('.resend-code a');

    if (!countdownElement || !resendLink) return;

    let seconds = 60;
    resendLink.style.pointerEvents = 'none';
    resendLink.style.opacity = '0.5';

    const interval = setInterval(() => {
        countdownElement.textContent = seconds;
        seconds--;

        if (seconds < 0) {
            clearInterval(interval);
            resendLink.style.pointerEvents = 'auto';
            resendLink.style.opacity = '1';
            document.getElementById('resend-countdown').style.display = 'none';
        }
    }, 1000);
}

// Resend verification code
function resendCode() {
    const pendingUser = JSON.parse(sessionStorage.getItem('pendingTwoFactorUser'));
    if (pendingUser) {
        sendTwoFactorCode(pendingUser);
        showNotification('تم إعادة إرسال رمز التحقق', 'info');
        document.getElementById('resend-countdown').style.display = 'block';
    }
}

// Send verification email
function sendVerificationEmail(user) {
    // Simulate sending verification email
    console.log(`Sending verification email to ${user.email}`);
}

// Check account lockout
function checkAccountLockout() {
    const attempts = parseInt(localStorage.getItem('loginAttempts') || '0');
    const lastAttempt = parseInt(localStorage.getItem('lastAttempt') || '0');
    const now = new Date().getTime();

    authState.loginAttempts = attempts;

    // Reset attempts after lockout period
    if (now - lastAttempt > authState.lockoutTime) {
        authState.loginAttempts = 0;
        localStorage.removeItem('loginAttempts');
        localStorage.removeItem('lastAttempt');
    }
}

// Check if account is locked
function isAccountLocked() {
    return authState.loginAttempts >= authState.maxLoginAttempts;
}

// Setup session timeout
function setupSessionTimeout() {
    if (!authState.isLoggedIn || authState.rememberMe) return;

    // Clear existing timeout
    if (window.sessionTimeoutId) {
        clearTimeout(window.sessionTimeoutId);
    }

    // Set new timeout
    window.sessionTimeoutId = setTimeout(() => {
        showNotification('انتهت جلسة العمل. يرجى تسجيل الدخول مرة أخرى.', 'warning');
        logout();
    }, authState.sessionTimeout);
}

// Modal functions
function showLoginModal() {
    const modal = document.getElementById('login-modal');
    if (modal) {
        modal.classList.add('show');
        document.getElementById('login-email').focus();
    }
}

function showRegisterModal() {
    const modal = document.getElementById('register-modal');
    if (modal) {
        modal.classList.add('show');
        document.getElementById('register-firstname').focus();
    }
}

function showForgotPassword() {
    closeAuthModal('login-modal');
    const modal = document.getElementById('forgot-password-modal');
    if (modal) {
        modal.classList.add('show');
        document.getElementById('forgot-email').focus();
    }
}

function showTwoFactorModal(user) {
    const modal = document.getElementById('two-factor-modal');
    if (modal) {
        modal.classList.add('show');

        // Update phone number display
        const phoneDisplay = modal.querySelector('p');
        if (phoneDisplay && user.phone) {
            const maskedPhone = user.phone.slice(0, -4) + '****';
            phoneDisplay.textContent = `تم إرسال رمز التحقق إلى رقم هاتفك المنتهي بـ ${maskedPhone}`;
        }

        // Focus first input
        const firstInput = modal.querySelector('.code-input');
        if (firstInput) {
            firstInput.focus();
        }
    }
}

function closeAuthModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');

        // Reset form
        const form = modal.querySelector('form');
        if (form) {
            form.reset();

            // Clear validation states
            const inputGroups = form.querySelectorAll('.input-group');
            inputGroups.forEach(group => {
                group.classList.remove('valid', 'invalid');
            });

            const validations = form.querySelectorAll('.field-validation');
            validations.forEach(validation => {
                validation.textContent = '';
                validation.className = 'field-validation';
            });

            // Reset password strength
            const strengthFill = form.querySelector('.strength-fill');
            if (strengthFill) {
                strengthFill.className = 'strength-fill';
            }

            // Reset code inputs
            const codeInputs = form.querySelectorAll('.code-input');
            codeInputs.forEach(input => {
                input.value = '';
                input.classList.remove('filled');
            });
        }
    }
}

function switchToLogin() {
    closeAuthModal('register-modal');
    closeAuthModal('forgot-password-modal');
    showLoginModal();
}

function switchToRegister() {
    closeAuthModal('login-modal');
    showRegisterModal();
}

// Toggle user menu
function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

// Close user menu when clicking outside
document.addEventListener('click', function(e) {
    const userMenu = document.getElementById('user-menu');
    const dropdown = document.getElementById('user-dropdown');

    if (userMenu && dropdown && !userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

// Toggle password visibility
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.nextElementSibling;
    const icon = toggle.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Social login functions
function loginWithGoogle() {
    showNotification('تسجيل الدخول بـ Google قيد التطوير', 'info');
}

function loginWithFacebook() {
    showNotification('تسجيل الدخول بـ Facebook قيد التطوير', 'info');
}

function registerWithGoogle() {
    showNotification('التسجيل بـ Google قيد التطوير', 'info');
}

function registerWithFacebook() {
    showNotification('التسجيل بـ Facebook قيد التطوير', 'info');
}

// User profile functions
function showProfile() {
    showNotification('صفحة الملف الشخصي قيد التطوير', 'info');
    toggleUserMenu();
}

function showOrderHistory() {
    showNotification('تاريخ الطلبات قيد التطوير', 'info');
    toggleUserMenu();
}

function showFavorites() {
    showNotification('المفضلة قيد التطوير', 'info');
    toggleUserMenu();
}

function showSettings() {
    showNotification('الإعدادات قيد التطوير', 'info');
    toggleUserMenu();
}

function showTerms() {
    showNotification('الشروط والأحكام قيد التطوير', 'info');
}

function showPrivacy() {
    showNotification('سياسة الخصوصية قيد التطوير', 'info');
}

// Button loading states
function showButtonLoading(button) {
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');

    if (btnText && btnLoading) {
        btnText.style.display = 'none';
        btnLoading.style.display = 'flex';
    }

    button.disabled = true;
}

function hideButtonLoading(button) {
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');

    if (btnText && btnLoading) {
        btnText.style.display = 'block';
        btnLoading.style.display = 'none';
    }

    button.disabled = false;
}

// Security features
function detectSuspiciousActivity() {
    // Monitor for suspicious login patterns
    const attempts = authState.loginAttempts;
    const threshold = 3;

    if (attempts >= threshold) {
        console.warn('Suspicious login activity detected');
        logSecurityEvent('suspicious_login_attempts', {
            attempts: attempts,
            timestamp: new Date().toISOString()
        });
    }
}

function logSecurityEvent(event, details) {
    // Log security events for monitoring
    console.log(`Security Event: ${event}`, details);

    // In real app, this would send to security monitoring system
    const securityLog = {
        event: event,
        details: details,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        ip: 'simulated_ip' // In real app, get from server
    };

    // Store locally for demo (in real app, send to server)
    const logs = JSON.parse(localStorage.getItem('securityLogs') || '[]');
    logs.push(securityLog);
    localStorage.setItem('securityLogs', JSON.stringify(logs.slice(-100))); // Keep last 100 logs
}

// Advanced security checks
function checkPasswordBreach(password) {
    // In real app, this would check against known breached passwords
    const commonPasswords = [
        '123456', 'password', '123456789', '12345678', '12345',
        '1234567', '1234567890', 'qwerty', 'abc123', 'password123'
    ];

    return commonPasswords.includes(password.toLowerCase());
}

function generateSecurePassword() {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    let password = '';

    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    return password;
}

// Device fingerprinting (basic)
function getDeviceFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);

    return {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        screen: `${screen.width}x${screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        canvas: canvas.toDataURL(),
        timestamp: new Date().toISOString()
    };
}

// Rate limiting
const rateLimiter = {
    attempts: {},

    isAllowed(action, maxAttempts = 5, windowMs = 15 * 60 * 1000) {
        const now = Date.now();
        const key = `${action}_${this.getClientId()}`;

        if (!this.attempts[key]) {
            this.attempts[key] = [];
        }

        // Remove old attempts outside the window
        this.attempts[key] = this.attempts[key].filter(time => now - time < windowMs);

        if (this.attempts[key].length >= maxAttempts) {
            return false;
        }

        this.attempts[key].push(now);
        return true;
    },

    getClientId() {
        // Simple client identification (in real app, use more sophisticated method)
        return localStorage.getItem('clientId') || this.generateClientId();
    },

    generateClientId() {
        const id = Math.random().toString(36).substr(2, 9);
        localStorage.setItem('clientId', id);
        return id;
    }
};

// Export functions for global access
window.authFunctions = {
    showLoginModal,
    showRegisterModal,
    showForgotPassword,
    closeAuthModal,
    switchToLogin,
    switchToRegister,
    toggleUserMenu,
    togglePassword,
    loginWithGoogle,
    loginWithFacebook,
    registerWithGoogle,
    registerWithFacebook,
    showProfile,
    showOrderHistory,
    showFavorites,
    showSettings,
    showTerms,
    showPrivacy,
    logout,
    resendCode,
    generateSecurePassword,
    getDeviceFingerprint
};

// Initialize security monitoring
document.addEventListener('DOMContentLoaded', function() {
    // Log page load
    logSecurityEvent('page_load', {
        page: window.location.pathname,
        referrer: document.referrer
    });

    // Monitor for suspicious activity
    setInterval(detectSuspiciousActivity, 30000); // Check every 30 seconds
});
