<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مطعم بازوكا - أفضل الأطباق الشرقية والغربية</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <i class="fas fa-utensils"></i>
                    <span>بازوكا</span>
                </div>
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">الرئيسية</a></li>
                    <li><a href="#menu" class="nav-link">القائمة</a></li>
                    <li><a href="#about" class="nav-link">من نحن</a></li>
                    <li><a href="#contact" class="nav-link">تواصل معنا</a></li>
                    <li><a href="#" class="nav-link cart-btn" onclick="toggleCart()">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">0</span>
                    </a></li>
                    <li class="admin-login-nav">
                        <a href="admin-login.html" class="nav-link admin-login-btn" title="تسجيل دخول الإدارة">
                            <i class="fas fa-user-shield"></i>
                            <span>الإدارة</span>
                        </a>
                    </li>
                    <li class="user-menu" id="user-menu">
                        <a href="#" class="nav-link user-btn" onclick="toggleUserMenu()">
                            <i class="fas fa-user"></i>
                            <span id="user-name">تسجيل دخول</span>
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="user-dropdown" id="user-dropdown">
                            <div class="user-dropdown-content">
                                <div class="guest-menu" id="guest-menu">
                                    <a href="#" onclick="showLoginModal()">
                                        <i class="fas fa-sign-in-alt"></i> تسجيل دخول
                                    </a>
                                    <a href="#" onclick="showRegisterModal()">
                                        <i class="fas fa-user-plus"></i> إنشاء حساب
                                    </a>
                                </div>
                                <div class="user-menu-logged" id="user-menu-logged" style="display: none;">
                                    <div class="user-info">
                                        <img src="" alt="صورة المستخدم" id="user-avatar" class="user-avatar">
                                        <div class="user-details">
                                            <span class="user-display-name" id="user-display-name"></span>
                                            <span class="user-email" id="user-email"></span>
                                        </div>
                                    </div>
                                    <hr>
                                    <a href="#" onclick="showProfile()">
                                        <i class="fas fa-user-edit"></i> الملف الشخصي
                                    </a>
                                    <a href="#" onclick="showOrderHistory()">
                                        <i class="fas fa-history"></i> تاريخ الطلبات
                                    </a>
                                    <a href="#" onclick="showFavorites()">
                                        <i class="fas fa-heart"></i> المفضلة
                                    </a>
                                    <div class="admin-menu-item" id="admin-menu-item" style="display: none;">
                                        <a href="dashboard.html">
                                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم المتقدمة
                                        </a>
                                    </div>
                                    <a href="#" onclick="showSettings()">
                                        <i class="fas fa-cog"></i> الإعدادات
                                    </a>
                                    <hr>
                                    <a href="#" onclick="logout()" class="logout-link">
                                        <i class="fas fa-sign-out-alt"></i> تسجيل خروج
                                    </a>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1 class="hero-title">مرحباً بكم في مطعم بازوكا</h1>
            <p class="hero-subtitle">تجربة طعام استثنائية مع أفضل الأطباق الشرقية والغربية</p>
            <div class="hero-buttons">
                <button class="btn btn-primary" onclick="scrollToSection('menu')">استكشف القائمة</button>
                <button class="btn btn-secondary" onclick="scrollToSection('contact')">احجز طاولة</button>
            </div>
        </div>
        <div class="hero-image">
            <img src="images/hero-food.jpg" alt="طعام شهي" id="hero-img">
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">لماذا نحن الأفضل؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <i class="fas fa-clock"></i>
                    <h3>توصيل سريع</h3>
                    <p>توصيل خلال 30 دقيقة أو أقل</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-leaf"></i>
                    <h3>مكونات طازجة</h3>
                    <p>نستخدم أجود المكونات الطازجة يومياً</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-star"></i>
                    <h3>جودة عالية</h3>
                    <p>أطباق مُعدة بأيدي أمهر الطهاة</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-money-bill-wave"></i>
                    <h3>أسعار مناسبة</h3>
                    <p>أفضل الأسعار مع جودة لا تُضاهى</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Menu Section -->
    <section id="menu" class="menu-section">
        <div class="container">
            <h2 class="section-title">قائمة الطعام</h2>
            <div class="menu-categories">
                <button class="category-btn active" data-category="all">الكل</button>
                <button class="category-btn" data-category="appetizers">المقبلات</button>
                <button class="category-btn" data-category="main">الأطباق الرئيسية</button>
                <button class="category-btn" data-category="desserts">الحلويات</button>
                <button class="category-btn" data-category="drinks">المشروبات</button>
            </div>
            <div class="menu-grid" id="menu-grid">
                <!-- Menu items will be loaded here by JavaScript -->
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>قصة مطعم بازوكا</h2>
                    <p>منذ تأسيسنا، نسعى لتقديم تجربة طعام لا تُنسى تجمع بين النكهات الأصيلة والإبداع في التحضير. مطعم بازوكا ليس مجرد مكان لتناول الطعام، بل وجهة لعشاق الطعام الراقي.</p>
                    <p>نفتخر بفريق من أمهر الطهاة الذين يحرصون على تقديم أطباق شهية باستخدام أجود المكونات الطازجة والتوابل المختارة بعناية.</p>
                    <div class="stats">
                        <div class="stat">
                            <span class="stat-number">1000+</span>
                            <span class="stat-label">عميل سعيد</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">طبق مميز</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">5</span>
                            <span class="stat-label">سنوات خبرة</span>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <img src="images/chef.jpg" alt="الطاهي الرئيسي">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <h2 class="section-title">تواصل معنا</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h3>العنوان</h3>
                            <p>شارع الملك فهد، الرياض، المملكة العربية السعودية</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h3>الهاتف</h3>
                            <p>+966 11 123 4567</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h3>البريد الإلكتروني</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-clock"></i>
                        <div>
                            <h3>ساعات العمل</h3>
                            <p>يومياً من 11:00 ص إلى 12:00 م</p>
                        </div>
                    </div>
                </div>
                <form class="contact-form" id="contact-form">
                    <h3>احجز طاولة</h3>
                    <input type="text" placeholder="الاسم" required>
                    <input type="tel" placeholder="رقم الهاتف" required>
                    <input type="email" placeholder="البريد الإلكتروني" required>
                    <input type="date" required>
                    <input type="time" required>
                    <select required>
                        <option value="">عدد الأشخاص</option>
                        <option value="1">شخص واحد</option>
                        <option value="2">شخصان</option>
                        <option value="3">3 أشخاص</option>
                        <option value="4">4 أشخاص</option>
                        <option value="5+">5 أشخاص أو أكثر</option>
                    </select>
                    <textarea placeholder="ملاحظات إضافية"></textarea>
                    <button type="submit" class="btn btn-primary">احجز الآن</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Cart Sidebar -->
    <div class="cart-sidebar" id="cart-sidebar">
        <div class="cart-header">
            <h3>سلة التسوق</h3>
            <button class="close-cart" onclick="toggleCart()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="cart-items" id="cart-items">
            <!-- Cart items will be added here -->
        </div>
        <div class="cart-footer">
            <div class="cart-total">
                <span>المجموع: <span id="cart-total">0</span> ريال</span>
            </div>
            <button class="btn btn-primary checkout-btn" onclick="checkout()">إتمام الطلب</button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <i class="fas fa-utensils"></i>
                        <span>بازوكا</span>
                    </div>
                    <p>مطعم بازوكا - تجربة طعام استثنائية</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="#home">الرئيسية</a></li>
                        <li><a href="#menu">القائمة</a></li>
                        <li><a href="#about">من نحن</a></li>
                        <li><a href="#contact">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> شارع الملك فهد، الرياض</p>
                    <p><i class="fas fa-phone"></i> +966 11 123 4567</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 مطعم بازوكا. جميع الحقوق محفوظة.</p>
                </div>
                <div class="developer-info">
                    <div class="developer-card">
                        <div class="developer-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="developer-details">
                            <p class="developer-title">تم التطوير والتصميم بواسطة</p>
                            <p class="developer-name">م. محمد الأشرافي</p>
                            <p class="developer-contact">
                                <i class="fas fa-phone"></i>
                                <a href="tel:+966532969067">0532969067</a>
                            </p>
                            <p class="developer-speciality">مطور ومصمم مواقع احترافي</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div class="modal auth-modal" id="login-modal">
        <div class="modal-content auth-modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h3>
                <button class="modal-close" onclick="closeAuthModal('login-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="login-form" class="auth-form">
                    <div class="form-group">
                        <label for="login-email">البريد الإلكتروني أو رقم الهاتف</label>
                        <div class="input-group">
                            <i class="fas fa-envelope"></i>
                            <input type="text" id="login-email" name="email" placeholder="أدخل البريد الإلكتروني أو رقم الهاتف" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="login-password">كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="login-password" name="password" placeholder="أدخل كلمة المرور" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('login-password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="remember-me" name="remember">
                            <span class="checkmark"></span>
                            تذكرني
                        </label>
                        <a href="#" onclick="showForgotPassword()" class="forgot-password">نسيت كلمة المرور؟</a>
                    </div>
                    <button type="submit" class="btn btn-primary auth-btn">
                        <span class="btn-text">تسجيل دخول</span>
                        <div class="btn-loading" style="display: none;">
                            <div class="loading-spinner"></div>
                            جاري التحقق...
                        </div>
                    </button>
                    <div class="auth-divider">
                        <span>أو</span>
                    </div>
                    <div class="social-login">
                        <button type="button" class="btn btn-social google-btn" onclick="loginWithGoogle()">
                            <i class="fab fa-google"></i>
                            تسجيل دخول بـ Google
                        </button>
                        <button type="button" class="btn btn-social facebook-btn" onclick="loginWithFacebook()">
                            <i class="fab fa-facebook-f"></i>
                            تسجيل دخول بـ Facebook
                        </button>
                    </div>
                    <div class="auth-switch">
                        <p>ليس لديك حساب؟ <a href="#" onclick="switchToRegister()">إنشاء حساب جديد</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Register Modal -->
    <div class="modal auth-modal" id="register-modal">
        <div class="modal-content auth-modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h3>
                <button class="modal-close" onclick="closeAuthModal('register-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="register-form" class="auth-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="register-firstname">الاسم الأول</label>
                            <div class="input-group">
                                <i class="fas fa-user"></i>
                                <input type="text" id="register-firstname" name="firstname" placeholder="الاسم الأول" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="register-lastname">الاسم الأخير</label>
                            <div class="input-group">
                                <i class="fas fa-user"></i>
                                <input type="text" id="register-lastname" name="lastname" placeholder="الاسم الأخير" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="register-email">البريد الإلكتروني</label>
                        <div class="input-group">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="register-email" name="email" placeholder="أدخل البريد الإلكتروني" required>
                            <div class="validation-icon"></div>
                        </div>
                        <div class="field-validation" id="email-validation"></div>
                    </div>
                    <div class="form-group">
                        <label for="register-phone">رقم الهاتف</label>
                        <div class="input-group">
                            <i class="fas fa-phone"></i>
                            <select class="country-code">
                                <option value="+966">🇸🇦 +966</option>
                                <option value="+971">🇦🇪 +971</option>
                                <option value="+965">🇰🇼 +965</option>
                                <option value="+973">🇧🇭 +973</option>
                                <option value="+974">🇶🇦 +974</option>
                            </select>
                            <input type="tel" id="register-phone" name="phone" placeholder="501234567" required>
                            <div class="validation-icon"></div>
                        </div>
                        <div class="field-validation" id="phone-validation"></div>
                    </div>
                    <div class="form-group">
                        <label for="register-password">كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="register-password" name="password" placeholder="أدخل كلمة المرور" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('register-password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <div class="strength-text">قوة كلمة المرور</div>
                        </div>
                        <div class="password-requirements">
                            <div class="requirement" id="req-length">
                                <i class="fas fa-times"></i> 8 أحرف على الأقل
                            </div>
                            <div class="requirement" id="req-uppercase">
                                <i class="fas fa-times"></i> حرف كبير واحد على الأقل
                            </div>
                            <div class="requirement" id="req-lowercase">
                                <i class="fas fa-times"></i> حرف صغير واحد على الأقل
                            </div>
                            <div class="requirement" id="req-number">
                                <i class="fas fa-times"></i> رقم واحد على الأقل
                            </div>
                            <div class="requirement" id="req-special">
                                <i class="fas fa-times"></i> رمز خاص واحد على الأقل
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="register-confirm-password">تأكيد كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="register-confirm-password" name="confirmPassword" placeholder="أعد إدخال كلمة المرور" required>
                            <div class="validation-icon"></div>
                        </div>
                        <div class="field-validation" id="confirm-password-validation"></div>
                    </div>
                    <div class="form-group">
                        <label for="register-birthdate">تاريخ الميلاد</label>
                        <div class="input-group">
                            <i class="fas fa-calendar"></i>
                            <input type="date" id="register-birthdate" name="birthdate" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="register-gender">الجنس</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="gender" value="male" required>
                                <span class="radio-custom"></span>
                                ذكر
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="gender" value="female" required>
                                <span class="radio-custom"></span>
                                أنثى
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label terms-label">
                            <input type="checkbox" id="accept-terms" name="terms" required>
                            <span class="checkmark"></span>
                            أوافق على <a href="#" onclick="showTerms()">الشروط والأحكام</a> و <a href="#" onclick="showPrivacy()">سياسة الخصوصية</a>
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="newsletter" name="newsletter">
                            <span class="checkmark"></span>
                            أرغب في تلقي العروض والأخبار عبر البريد الإلكتروني
                        </label>
                    </div>
                    <button type="submit" class="btn btn-primary auth-btn">
                        <span class="btn-text">إنشاء حساب</span>
                        <div class="btn-loading" style="display: none;">
                            <div class="loading-spinner"></div>
                            جاري إنشاء الحساب...
                        </div>
                    </button>
                    <div class="auth-divider">
                        <span>أو</span>
                    </div>
                    <div class="social-login">
                        <button type="button" class="btn btn-social google-btn" onclick="registerWithGoogle()">
                            <i class="fab fa-google"></i>
                            التسجيل بـ Google
                        </button>
                        <button type="button" class="btn btn-social facebook-btn" onclick="registerWithFacebook()">
                            <i class="fab fa-facebook-f"></i>
                            التسجيل بـ Facebook
                        </button>
                    </div>
                    <div class="auth-switch">
                        <p>لديك حساب بالفعل؟ <a href="#" onclick="switchToLogin()">تسجيل دخول</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="modal auth-modal" id="forgot-password-modal">
        <div class="modal-content auth-modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-key"></i> استعادة كلمة المرور</h3>
                <button class="modal-close" onclick="closeAuthModal('forgot-password-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="forgot-password-form" class="auth-form">
                    <div class="form-group">
                        <label for="forgot-email">البريد الإلكتروني</label>
                        <div class="input-group">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="forgot-email" name="email" placeholder="أدخل البريد الإلكتروني المسجل" required>
                        </div>
                        <small class="form-help">سنرسل لك رابط استعادة كلمة المرور على هذا البريد</small>
                    </div>
                    <button type="submit" class="btn btn-primary auth-btn">
                        <span class="btn-text">إرسال رابط الاستعادة</span>
                        <div class="btn-loading" style="display: none;">
                            <div class="loading-spinner"></div>
                            جاري الإرسال...
                        </div>
                    </button>
                    <div class="auth-switch">
                        <p>تذكرت كلمة المرور؟ <a href="#" onclick="switchToLogin()">تسجيل دخول</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Two Factor Authentication Modal -->
    <div class="modal auth-modal" id="two-factor-modal">
        <div class="modal-content auth-modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-shield-alt"></i> التحقق بخطوتين</h3>
                <button class="modal-close" onclick="closeAuthModal('two-factor-modal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="two-factor-content">
                    <div class="two-factor-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4>أدخل رمز التحقق</h4>
                    <p>تم إرسال رمز التحقق إلى رقم هاتفك المنتهي بـ ***1234</p>
                    <form id="two-factor-form" class="auth-form">
                        <div class="verification-code">
                            <input type="text" maxlength="1" class="code-input" data-index="0">
                            <input type="text" maxlength="1" class="code-input" data-index="1">
                            <input type="text" maxlength="1" class="code-input" data-index="2">
                            <input type="text" maxlength="1" class="code-input" data-index="3">
                            <input type="text" maxlength="1" class="code-input" data-index="4">
                            <input type="text" maxlength="1" class="code-input" data-index="5">
                        </div>
                        <div class="resend-code">
                            <p>لم تستلم الرمز؟ <a href="#" onclick="resendCode()">إعادة إرسال</a></p>
                            <div class="countdown" id="resend-countdown">إعادة الإرسال خلال: <span>60</span> ثانية</div>
                        </div>
                        <button type="submit" class="btn btn-primary auth-btn">
                            <span class="btn-text">تحقق</span>
                            <div class="btn-loading" style="display: none;">
                                <div class="loading-spinner"></div>
                                جاري التحقق...
                            </div>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="scripts/main.js"></script>
    <script src="scripts/menu.js"></script>
    <script src="scripts/cart.js"></script>
    <script src="scripts/auth.js"></script>
</body>
</html>
